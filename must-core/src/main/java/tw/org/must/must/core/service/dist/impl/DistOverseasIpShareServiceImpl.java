package tw.org.must.must.core.service.dist.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.dist.DistOverseasIpShareService;
import tw.org.must.must.mapper.dist.DistOverseasIpShareMapper;
import tw.org.must.must.model.dist.DistOverseasIpShare;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DistOverseasIpShareServiceImpl extends BaseServiceImpl<DistOverseasIpShare>
		implements DistOverseasIpShareService {

	private final DistOverseasIpShareMapper distOverseasIpShareMapper;

	@Autowired
	public DistOverseasIpShareServiceImpl(DistOverseasIpShareMapper distOverseasIpShareMapper) {
		super(distOverseasIpShareMapper);
		this.distOverseasIpShareMapper = distOverseasIpShareMapper;
	}

	@Override
	public List<DistOverseasIpShare> getByDistNoAndWorkUniqueKey(String distNo, String workUniqueKey) {
		Example example = new Example(DistOverseasIpShare.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distNo", distNo);
        criteria.andEqualTo("workUniqueKey", workUniqueKey);
        return distOverseasIpShareMapper.selectByExample(example);
	}
}