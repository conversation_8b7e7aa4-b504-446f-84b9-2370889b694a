package tw.org.must.must.core.service.dist.impl;

import org.springframework.stereotype.Service;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.dist.DistOverseasCheckingIpNotInWorkService;
import tw.org.must.must.mapper.dist.DistOverseasCheckingIpNotInWorkMapper;
import tw.org.must.must.model.report.DistOverseasCheckingIpNotInWork;

import java.util.List;

@Service
public class DistOverseasCheckingIpNotInWorkServiceImpl extends BaseServiceImpl<DistOverseasCheckingIpNotInWork> implements DistOverseasCheckingIpNotInWorkService {

    private DistOverseasCheckingIpNotInWorkMapper distOverseasCheckingIpNotInWorkMapper;

    public DistOverseasCheckingIpNotInWorkServiceImpl(DistOverseasCheckingIpNotInWorkMapper distOverseasCheckingIpNotInWorkMapper) {
        super(distOverseasCheckingIpNotInWorkMapper);
        this.distOverseasCheckingIpNotInWorkMapper = distOverseasCheckingIpNotInWorkMapper;
    }

    @Override
    public List<DistOverseasCheckingIpNotInWork> selectByDistNo(String distNo) {
        return null;
    }
}
