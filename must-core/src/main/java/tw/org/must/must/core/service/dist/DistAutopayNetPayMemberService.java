package tw.org.must.must.core.service.dist;


import net.sf.jasperreports.engine.JRException;
import tw.org.must.must.model.dist.DistAutopayNetPayMember;
import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.dist.vo.DistAutoPay750Request;
import tw.org.must.must.model.dist.vo.Fsris100Request;
import tw.org.must.must.model.dist.vo.v750.DistAutoPay750Vo;
import tw.org.must.must.model.dist.vo.IncomeMemberNetVo;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DistAutopayNetPayMemberService extends BaseService<DistAutopayNetPayMember> {

    List<DistAutopayNetPayMember> lisDistAutopayNetPayMember(Integer pageNum, Integer pageSize, String ipBaseNo, String ipType, String paName, String distNo);

    List<DistAutopayNetPayMember> lisDistAutopayNetPayMemberForPay(Integer pageNum, Integer pageSize, String autopayNo, String ipBaseNo, String ipType, String paNameNo, String paName, String distNo, Integer statYear, Integer endYear);

    IncomeMemberNetVo lisDistAutopayNetPayMember(String autopayNo, String ipBaseNo, String ipType, String paNameNo, String paName, String distNo, Integer statYear, Integer endYear);

    Integer distAutopayNetPayMemberSetInvNo(DistAutopayNetPayMember distAutopayNetPayMember);

    List<Map<String, Object>> groupDistAutopayNetPayMember(String ipBaseNo, String startTime, String endTime);

    Integer insertList(List<DistAutopayNetPayMember> distAutopayNetPayMemberList);

    List<DistAutopayNetPayMember> selectByAutoPay(String autopayNo,String paNameNo,String distNo, List<String> list);
    List<DistAutopayNetPayMember> selectByAutoPayGroupByIpBaseNo(String autopayNo, String paNameNo, String distNo, List<String> list);

    List<DistAutopayNetPayMember> selectByAutoPay(String autopayNo, List<String> paNameNoList, String distNo, List<String> list, Date payDate);

    List<DistAutopayNetPayMember> selectNetPayMemberForFsris100(String autopayNo, List<String> paNameNoList, List<String> distNos, Date starDate, Date endDate);

    Integer redo(Long id);

    List<DistAutopayNetPayMember> getIpBaseNoList(String paymentNo, List<String> ipBaseNoList);

    List<DistAutoPay750Vo> getDistAutoPay750List(String autopayNo);
    List<DistAutoPay750Vo> getDistAutoPay750List(String autopayNo, String orderWay, List<String> paNameNoList);
    List<DistAutoPay750Vo> getDistAutoPay880List(String autopayNo,String ipBaseNo,String paNameNo, String distNo);

    void reportDistAutoPay750(DistAutoPay750Request request, HttpServletResponse httpServletResponse) throws FileNotFoundException, JRException;
    void reportDistAutoPay780(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, Integer societyCode, String distNo, Boolean bankInfo, String autopayDate, Boolean groupBySociety) throws FileNotFoundException, JRException;
    void reportDistAutoPay880(String autopayNo, String detail, HttpServletResponse httpServletResponse,String ipBaseNo,String paNameNo, String distNo) throws FileNotFoundException, JRException;
    void reportDistAutoPayMem800(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String paNameNo, String distNo) throws Exception;
    void reportDistAutoPayMemCheck730(HttpServletResponse httpServletResponse, String autopayNo, List<String> paNameNoList, String distNo, Date payDate) throws FileNotFoundException, JRException;
    void reportDistAutoPayMemCheck730(DistAutopayNetPayMember distAutopayNetPayMember);
    void reportDistAutoPayMem730(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String paNameNo, String distNo) throws FileNotFoundException, JRException;

    /**
     * sales tax report（DIVA700）团体会员请款单
     *
     * @param paNameNoList
     * @return
     */
    void exportSaleTaxReport(HttpServletResponse httpServletResponse, List<String> paNameNoList) throws JRException, FileNotFoundException;

    Integer delete(String autopayNo);

    void exportTxtReport(HttpServletResponse httpServletResponse, String autopayNo);

    void exportFSRIS100Report(HttpServletResponse httpServletResponse, Fsris100Request fsris100Request);
}