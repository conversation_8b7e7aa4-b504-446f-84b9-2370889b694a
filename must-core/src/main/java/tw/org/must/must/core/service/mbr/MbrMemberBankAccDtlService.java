package tw.org.must.must.core.service.mbr;


import java.util.Date;
import java.util.List;
import tw.org.must.must.model.mbr.MbrMemberBankAccDtl;
import tw.org.must.must.common.base.BaseService;

public interface MbrMemberBankAccDtlService extends BaseService<MbrMemberBankAccDtl> {

    void deleteByIpBaseNo(String ipBaseNo);

    List<MbrMemberBankAccDtl> selectByIpBaseNo(String ipBaseNo);

    Integer addOrUpdateList(List<MbrMemberBankAccDtl> mbrMemberBankAccDtlList);


    /**
     * 根据ipBaseNo查询会员的银行地址信息
     */
    MbrMemberBankAccDtl selectBankAddressByIpBaseNo(String ipBaseNo);
}