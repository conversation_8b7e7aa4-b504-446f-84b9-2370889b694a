package tw.org.must.must.core.service.dist;

import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.dist.DistCalcWorkIpShare;
import tw.org.must.must.model.dist.DistOverseasIpShare;

import java.util.List;
import java.util.Map;

public interface DistOverseasIpShareService extends BaseService<DistOverseasIpShare> {

    List<DistOverseasIpShare> getByDistNoAndWorkUniqueKey(String distNo, String workUniqueKey);
}