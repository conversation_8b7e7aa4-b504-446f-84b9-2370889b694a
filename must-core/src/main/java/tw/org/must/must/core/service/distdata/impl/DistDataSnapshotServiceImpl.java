package tw.org.must.must.core.service.distdata.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.core.service.distdata.*;
import tw.org.must.must.core.service.ref.RefSysSocService;
import tw.org.must.must.core.service.wrk.*;
import tw.org.must.must.model.distdata.*;
import tw.org.must.must.model.list.ListMatchDataDspDone;
import tw.org.must.must.model.ref.RefSysSoc;
import tw.org.must.must.model.wrk.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static tw.org.must.must.core.aspect.LogAspect.LOGGER;

@Service
public class DistDataSnapshotServiceImpl implements DistDataSnapshotService {

	@Autowired
	private WrkWorkService wrkWorkService;
	@Autowired
	private WrkWorkTitleService wrkWorkTitleService;

	@Autowired
	private WrkWorkArtistMergeService wrkWorkArtistMergeService;
	@Autowired
	private WrkArtistService wrkArtistService;
	
	@Autowired
	private WrkWorkComponentService wrkWorkComponentService;

	@Autowired
	private RefSysSocService refSysSocService;
	
	/**********************************************************************/

	@Autowired
	private DistDataSnapshotWorkService distDataSnapshotWorkService;
	@Autowired
	private DistDataSnapshotWorkTitleService distDataSnapshotWorkTitleService;
	@Autowired
	private DistDataSnapshotWorkIpShareService distDataSnapshotWorkIpShareService;

	@Autowired
	private DistDataSnapshotWorkArtistMergeService distDataSnapshotWorkArtistMergeService;
	@Autowired
	private DistDataSnapshotWorkArtistService distDataSnapshotWorkArtistService;
	@Autowired
	private DistDataSnapshotWorkComponentService distDataSnapshotWorkComponentService;

	@Override
	public WrkWork copyDistDataSnapshot(String distNo,Long ccidHeaderId,String workUniqueKey) {
		WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkUniqueKey(workUniqueKey);
		if(wrkWork == null){
			return null;
		}
		List<WrkWorkTitle> wrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKey(workUniqueKey);
		/* List<WrkWorkArtistMerge> wrkWorkArtistMergeList = wrkWorkArtistMergeService
				.getWrkWorkArtistMergeByWorkUniqueKey(workUniqueKey);
		List<Long> aritistIdList = new ArrayList<>(
				wrkWorkArtistMergeList.stream().map(WrkWorkArtistMerge::getAritistId).collect(Collectors.toSet()));
		List<WrkArtist> wrkArtistList = wrkArtistService.getWrkArtistByIdList(aritistIdList);
		List<WrkWorkComponent> wrkWorkComponentList = wrkWorkComponentService.getWrkWorkComponentServiceByWorkUniqueKey(workUniqueKey);
		// 拷贝数据

		copyDistDataSnapshotWork(distNo,ccidHeaderId, wrkWork);
		copyDistDataSnapshotWorkTitle(distNo,ccidHeaderId,wrkWorkTitle);
		copyDistDataSnapshotWorkArtistMerge(distNo,ccidHeaderId,wrkWorkArtistMergeList);
		copyDistDataSnapshotWorkArtist(distNo,ccidHeaderId,wrkArtistList);
		copyDistDataSnapshotWorkComponent(distNo,ccidHeaderId,wrkWorkComponentList);*/
		if(CollectionUtils.isNotEmpty(wrkWorkTitle) ){
			wrkWork.setGenre(wrkWorkTitle.get(0).getGenreCode());
		}
		return wrkWork;
	}

	@Override
	public DistDataSnapshotWork copyDistDataSnapshotNew(WrkWork wrkWork,Long ccidHeaderId, ListMatchDataDspDone listMatchDataDspDone) {
		if(wrkWork == null || listMatchDataDspDone==null){
			return null;
		}
		// listMatchDataDspId
		Long fileMappingId = listMatchDataDspDone.getFileMappingId();
		DistDataSnapshotWork distDataSnapshotWork = new DistDataSnapshotWork();
		distDataSnapshotWork.init();
		BeanUtils.copyProperties(wrkWork, distDataSnapshotWork);
		distDataSnapshotWork.setId(null);
		distDataSnapshotWork.setIsDist(wrkWork.getSd());
		if (distDataSnapshotWork.getIsDist()==null){
			distDataSnapshotWork.setIsDist("N");
		}
		distDataSnapshotWork.setCcidClaimHeaderId(ccidHeaderId);

		// 来自于dsp_done表的主键
		Long listMatchDataDspDoneId = listMatchDataDspDone.getId();
		distDataSnapshotWork.setListMatchDataDspDoneId(listMatchDataDspDoneId);
		// listdspfileBaseId
		Long fileBaseId = listMatchDataDspDone.getFileBaseId();
		distDataSnapshotWork.setListDspFileBaseId(fileBaseId);
		distDataSnapshotWork.setListDspFileDataMappingId(fileMappingId);
		String releaseId = listMatchDataDspDone.getReleaseId();
		distDataSnapshotWork.setReleaseId(releaseId);
		String resourceId = listMatchDataDspDone.getResourceId();
		distDataSnapshotWork.setResourceId(resourceId);
		Date listFileStartTime = listMatchDataDspDone.getListFileStartTime();
		distDataSnapshotWork.setListFileStartTime(listFileStartTime);
		Date listFileEndTime = listMatchDataDspDone.getListFileEndTime();
		distDataSnapshotWork.setListFileEndTime(listFileEndTime);
		String matchIsrc = listMatchDataDspDone.getMatchIsrc();
		distDataSnapshotWork.setIsrc(matchIsrc);
		String matchIswc = listMatchDataDspDone.getMatchIswc();
		distDataSnapshotWork.setIswc(matchIswc);
		String groupCode = listMatchDataDspDone.getGroupCode();
		distDataSnapshotWork.setBlockId(groupCode);
		//
		String extJson = listMatchDataDspDone.getExtJson();
		distDataSnapshotWork.setExtJson(extJson);

		BigDecimal clickNumber = listMatchDataDspDone.getClickNumber(); // usages
		if(null == clickNumber){
			clickNumber = BigDecimal.ZERO;
		}
		distDataSnapshotWork.setUsages(clickNumber);

		BigDecimal workPrice = listMatchDataDspDone.getWorkPrice();// netRevenue
		if(workPrice == null){
			workPrice = BigDecimal.ZERO;
		}
		distDataSnapshotWork.setNetRevenue(workPrice);
		return distDataSnapshotWork;
	}

	private void copyDistDataSnapshotWorkComponent(String distNo,Long ccidHeaderId, List<WrkWorkComponent> wrkWorkComponentList) {
		if (wrkWorkComponentList == null || wrkWorkComponentList.isEmpty()) {
			return;
		}
		List<DistDataSnapshotWorkComponent> distDataSnapshotWorkComponentList = new ArrayList<>();
		for (WrkWorkComponent wrkWorkComponent : wrkWorkComponentList) {
			DistDataSnapshotWorkComponent distDataSnapshotWorkComponent = new DistDataSnapshotWorkComponent();
			BeanUtils.copyProperties(wrkWorkComponent, distDataSnapshotWorkComponent);
			distDataSnapshotWorkComponent.init();
			distDataSnapshotWorkComponent.setId(null);
			distDataSnapshotWorkComponent.setDistNo(distNo);
			distDataSnapshotWorkComponent.setCcidClaimHeaderId(ccidHeaderId);
			distDataSnapshotWorkComponentList.add(distDataSnapshotWorkComponent);
		}
		distDataSnapshotWorkComponentService.addList(distDataSnapshotWorkComponentList);
	}

	private void copyDistDataSnapshotWorkArtist(String distNo,Long ccidHeaderId, List<WrkArtist> wrkArtistList) {
		if (wrkArtistList == null || wrkArtistList.isEmpty()) {
			return;
		}
		List<DistDataSnapshotWorkArtist> distDataSnapshotWorkArtistList = new ArrayList<>();
		for (WrkArtist wrkArtist : wrkArtistList) {
			DistDataSnapshotWorkArtist distDataSnapshotWorkArtist = new DistDataSnapshotWorkArtist();
			BeanUtils.copyProperties(wrkArtist, distDataSnapshotWorkArtist);
			distDataSnapshotWorkArtist.init();
			distDataSnapshotWorkArtist.setId(null);
			distDataSnapshotWorkArtist.setDistNo(distNo);
			distDataSnapshotWorkArtist.setCcidClaimHeaderId(ccidHeaderId);
			distDataSnapshotWorkArtistList.add(distDataSnapshotWorkArtist);
		}
		distDataSnapshotWorkArtistService.addList(distDataSnapshotWorkArtistList);
	}

	private void copyDistDataSnapshotWorkArtistMerge(String distNo, Long ccidHeaderId, List<WrkWorkArtistMerge> wrkWorkArtistMergeList) {
		if (wrkWorkArtistMergeList == null || wrkWorkArtistMergeList.isEmpty()) {
			return;
		}
		List<DistDataSnapshotWorkArtistMerge> distDataSnapshotWorkArtistMergeList = new ArrayList<>();
		for (WrkWorkArtistMerge wrkWorkArtistMerge : wrkWorkArtistMergeList) {
			DistDataSnapshotWorkArtistMerge distDataSnapshotWorkArtistMerge = new DistDataSnapshotWorkArtistMerge();
			BeanUtils.copyProperties(wrkWorkArtistMerge, distDataSnapshotWorkArtistMerge);
			distDataSnapshotWorkArtistMerge.init();
			distDataSnapshotWorkArtistMerge.setId(null);
			distDataSnapshotWorkArtistMerge.setDistNo(distNo);
			distDataSnapshotWorkArtistMerge.setCcidClaimHeaderId(ccidHeaderId);
			distDataSnapshotWorkArtistMergeList.add(distDataSnapshotWorkArtistMerge);
		}
		distDataSnapshotWorkArtistMergeService.addList(distDataSnapshotWorkArtistMergeList);
	}

	private void copyDistDataSnapshotWork(String distNo, Long ccidHeaderId, WrkWork wrkWork) {
		if (wrkWork==null) {
			return;
		}
		DistDataSnapshotWork distDataSnapshotWork = new DistDataSnapshotWork();
		BeanUtils.copyProperties(wrkWork, distDataSnapshotWork);
		distDataSnapshotWork.init();
		distDataSnapshotWork.setId(null);
		distDataSnapshotWork.setDistNo(distNo);
		distDataSnapshotWork.setIsDist(wrkWork.getSd());
		if (distDataSnapshotWork.getIsDist() ==null){
			distDataSnapshotWork.setIsDist("N");
		}
		distDataSnapshotWork.setCcidClaimHeaderId(ccidHeaderId);
		distDataSnapshotWorkService.add(distDataSnapshotWork);
	}

	private void copyDistDataSnapshotWorkTitle(String distNo, Long ccidHeaderId, List<WrkWorkTitle> wrkWorkTitleList) {
		if (wrkWorkTitleList == null || wrkWorkTitleList.isEmpty()) {
			return;
		}
		List<DistDataSnapshotWorkTitle> distDataSnapshotWorkTitleList = new ArrayList<DistDataSnapshotWorkTitle>();
		for (WrkWorkTitle wrkWorkTitle : wrkWorkTitleList) {
			DistDataSnapshotWorkTitle distDataSnapshotWorkTitle = new DistDataSnapshotWorkTitle();
			BeanUtils.copyProperties(wrkWorkTitle, distDataSnapshotWorkTitle);
			distDataSnapshotWorkTitle.init();
			distDataSnapshotWorkTitle.setId(null);
			distDataSnapshotWorkTitle.setDistNo(distNo);
			distDataSnapshotWorkTitle.setCcidClaimHeaderId(ccidHeaderId);
			distDataSnapshotWorkTitleList.add(distDataSnapshotWorkTitle);
		}
		distDataSnapshotWorkTitleService.addList(distDataSnapshotWorkTitleList);
	}

	@Override
	public void copyDistDataSnapshotWorkIpShare(String distNo,Long ccidHeaderId, List<WrkWorkIpShare> wrkWorkIpShareList) {
		if (wrkWorkIpShareList == null || wrkWorkIpShareList.isEmpty()) {
			return;
		}
		List<DistDataSnapshotWorkIpShare> distDataSnapshotWorkIpShareList = new ArrayList<DistDataSnapshotWorkIpShare>();
		for (WrkWorkIpShare wrkWorkIpShare : wrkWorkIpShareList) {
            DistDataSnapshotWorkIpShare distDataSnapshotWorkIpShare = new DistDataSnapshotWorkIpShare();
            BeanUtils.copyProperties(wrkWorkIpShare, distDataSnapshotWorkIpShare);
            distDataSnapshotWorkIpShare.init();
            distDataSnapshotWorkIpShare.setId(null);
            distDataSnapshotWorkIpShare.setDistNo(distNo);
            distDataSnapshotWorkIpShare.setCcidClaimHeaderId(ccidHeaderId);//TODO distDataSnapshotWorkIpShare.setWorkIpShare(wrkWorkIpShare.getIpShare());
			distDataSnapshotWorkIpShare.setWorkIpShare(wrkWorkIpShare.getIpShare());
			distDataSnapshotWorkIpShare.setIpSocietyCode(wrkWorkIpShare.getIpSocietyCode());
			String ipName = wrkWorkIpShare.getChineseName();
			if(StringUtils.isNotBlank(ipName)){
				ipName = ipName + "(" + wrkWorkIpShare.getName() + ")";
			} else {
				ipName = wrkWorkIpShare.getName();
			}
			distDataSnapshotWorkIpShare.setIpName(ipName);
            distDataSnapshotWorkIpShareList.add(distDataSnapshotWorkIpShare);
        }
		distDataSnapshotWorkIpShareService.addList(distDataSnapshotWorkIpShareList);
	}

	@Override
	public List<DistDataSnapshotWorkIpShare> copyDistDataSnapshotWorkIpShareNew(String distNo,Long ccidHeaderId, List<WrkWorkIpShare> wrkWorkIpShareList) {
		if (wrkWorkIpShareList == null || wrkWorkIpShareList.isEmpty()) {
			return null;
		}
		List<DistDataSnapshotWorkIpShare> distDataSnapshotWorkIpShareList = new ArrayList<DistDataSnapshotWorkIpShare>();
		for (WrkWorkIpShare wrkWorkIpShare : wrkWorkIpShareList) {
			DistDataSnapshotWorkIpShare distDataSnapshotWorkIpShare = new DistDataSnapshotWorkIpShare();
			if(StringUtils.isBlank(wrkWorkIpShare.getSd())){
				wrkWorkIpShare.setSd("N");
			}
			BeanUtils.copyProperties(wrkWorkIpShare, distDataSnapshotWorkIpShare);
			distDataSnapshotWorkIpShare.init();
			distDataSnapshotWorkIpShare.setId(null);
			distDataSnapshotWorkIpShare.setDistNo(distNo);
			distDataSnapshotWorkIpShare.setWorkIpShare(wrkWorkIpShare.getIpShare());
			distDataSnapshotWorkIpShare.setCcidClaimHeaderId(ccidHeaderId);
			distDataSnapshotWorkIpShareList.add(distDataSnapshotWorkIpShare);
		}
		return distDataSnapshotWorkIpShareList;
	}

	@Override
	public List<DistDataSnapshotWorkIpShare> getDistDataSnapshotWorkIpShare(String distNo, String workUniqueKey, String rigthType) {
		return distDataSnapshotWorkIpShareService.getDistDataSnapshotWorkIpShare(distNo,workUniqueKey,rigthType);
	}

	@Override
	public Integer clearDistDataSnapshot(String distNo) {
		distDataSnapshotWorkService.clearDistDataSnapshot(distNo);
		distDataSnapshotWorkTitleService.clearDistDataSnapshot(distNo);
		distDataSnapshotWorkIpShareService.clearDistDataSnapshot(distNo);
		distDataSnapshotWorkArtistMergeService.clearDistDataSnapshot(distNo);
		distDataSnapshotWorkArtistService.clearDistDataSnapshot(distNo);
		distDataSnapshotWorkComponentService.clearDistDataSnapshot(distNo);
		return 1;
	}

}