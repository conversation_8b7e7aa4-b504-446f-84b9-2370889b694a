package tw.org.must.must.core.service.listoverseas.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.core.handle.MatchMd5;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasMappingService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.mbr.MbrIpService;
import tw.org.must.must.core.service.mbr.MbrIpStatusService;
import tw.org.must.must.core.service.wrk.WrkWorkIpShareService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.core.service.wrk.WrkWorkTitleService;
import tw.org.must.must.mapper.list.ListMatchDataOverseasUniqMapper;
import tw.org.must.must.mapper.listoverseas.ListMatchDataOverseasMapper;
import tw.org.must.must.mapper.listoverseas.ListMatchDataOverseasMappingMapper;
import tw.org.must.must.mapper.listoverseas.ListOverseasFileBaseMapper;
import tw.org.must.must.model.list.ListMatchDataOverseasUniq;
import tw.org.must.must.model.list.vo.ListMatchDataOverseasMappingExport;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseas;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;
import tw.org.must.must.model.listoverseas.ListOverseasFileBase;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.mbr.MbrIpStatus;
import tw.org.must.must.model.mbr.vo.IpNameVO;
import tw.org.must.must.model.report.DistOverseasCheckingIpNotInWork;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static tw.org.must.must.common.constants.Constants.*;

@Service
public class ListMatchDataOverseasMappingServiceImpl extends BaseServiceImpl<ListMatchDataOverseasMapping>
        implements ListMatchDataOverseasMappingService {

    private final ListMatchDataOverseasMappingMapper listMatchDataOverseasMappingMapper;

    @Lazy
    @Autowired
    private WrkWorkService wrkWorkService;
    @Autowired
    private MbrIpService mbrIpService;
//    @Autowired
//    private CalcIpShareService calcIpShareService;
    @Autowired
    private WrkWorkTitleService wrkWorkTitleService ;
    @Autowired
    private MbrIpNameService mbrIpNameService;
    @Autowired
    private MbrIpStatusService mbrIpStatusService;

    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;

    @Autowired
    private ListMatchDataOverseasMapper listMatchDataOverseasMapper;

    @Autowired
    private ListOverseasFileBaseMapper listOverseasFileBaseMapper;

    @Autowired
    private ListMatchDataOverseasUniqMapper listMatchDataOverseasUniqMapper;

    private Map<String,List<WrkWorkIpShare>> wrkWorkIpshareMap = new HashMap<>();

    private Map<String,String> paNameNoMap = new HashMap<>();

    @Autowired
    public ListMatchDataOverseasMappingServiceImpl(
            ListMatchDataOverseasMappingMapper listMatchDataOverseasMappingMapper) {
        super(listMatchDataOverseasMappingMapper);
        this.listMatchDataOverseasMappingMapper = listMatchDataOverseasMappingMapper;
    }

    @Override
    public Integer saveList(List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList) {
        if (listMatchDataOverseasMappingList == null || listMatchDataOverseasMappingList.isEmpty()) {
            return 0;
        }
        return listMatchDataOverseasMappingMapper.insertList(listMatchDataOverseasMappingList);
    }

    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingList(Long fileMappingId) {
        if (fileMappingId == null || fileMappingId < 1L) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("fileMappingId", fileMappingId);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

    @Override
    public List<Long> getIds(String dataUniqueKey) {
        return listMatchDataOverseasMappingMapper.getIds(dataUniqueKey);
    }

/*    @Override
    public Integer checkListMatchDataOverseasMapping(
            List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList) {
        if (listMatchDataOverseasMappingList != null && listMatchDataOverseasMappingList.size() > 0) {
            for (ListMatchDataOverseasMapping listMatchDataOverseasMapping : listMatchDataOverseasMappingList) {
                String ipNameNo = listMatchDataOverseasMapping.getIpNameNo();
                List<MbrIpIndex> mbrIpIndexList = mbrIpService.getEsMbrIpIndexById(null, ipNameNo);
                if (mbrIpIndexList != null && mbrIpIndexList.size() > 0) {
                    MbrIpIndex mbrIpIndex = mbrIpIndexList.get(0);
                    //TODO 逻辑不多，不应该从ES中取数据，需要实时计算得出，暂时搁置
                    ///listMatchDataOverseasMapping.setIpSocietyCode(mbrIpIndex.getSociety_code());
                    listMatchDataOverseasMapping.setIpName(mbrIpIndex.getName());
                }
                listMatchDataOverseasMappingMapper.updateByPrimaryKeySelective(listMatchDataOverseasMapping);
            }
        }

        return 0;
    }*/

    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByOverseasIds(List<Long> overseasIds){
        if (CollectionUtils.isEmpty(overseasIds)) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("overseasId", overseasIds);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByOverseasId(Long overseasId) {
        if (overseasId == null || overseasId < 1L) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("overseasId", overseasId);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByMappingIds(List<Long> fileMappingIds) {
        if (CollectionUtils.isEmpty(fileMappingIds)) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("fileMappingId", fileMappingIds);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

/*    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByMappingId(Long mappingId) {
        if (mappingId == null || mappingId < 1L) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("fileMappingId", mappingId);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }*/

/*
    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByFileMappingIdListAndStatus(List<Long> fileMappingIdList, int status) {
        if (fileMappingIdList == null || fileMappingIdList.isEmpty()) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("fileMappingId", fileMappingIdList);
        criteria.andEqualTo("status", status);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }
*/


    @Override
    public void checkAndSetStatusAuto(ListMatchDataOverseasMapping listMatchDataOverseasMapping) {
        Long workId = listMatchDataOverseasMapping.getMatchWorkId();
        Integer soc = listMatchDataOverseasMapping.getMatchWorkSocietyCode();
        String workUniqueKey = Constants.getWorkUniqueKey(soc,workId);
        if(wrkWorkIpshareMap.containsKey(workUniqueKey)){
            List<WrkWorkIpShare> wrkWorkIpShareByYear = wrkWorkIpshareMap.get(workUniqueKey);
            Integer status = getStatus(listMatchDataOverseasMapping, wrkWorkIpShareByYear);
            listMatchDataOverseasMapping.setStatus(status);
            return;
        }

        List<WrkWorkIpShare> wrkWorkIpShareByYear = checkAndSetStatus(listMatchDataOverseasMapping);
        wrkWorkIpshareMap.put(workUniqueKey,wrkWorkIpShareByYear);
    }

    @Override
    public void checkAndSetStatusAuto(WrkWork wrkWork, WrkWorkTitle workTitle, List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList) {

        if(CollectionUtils.isEmpty(listMatchDataOverseasMappingList)){
            return;
        }

        List<WrkWorkIpShare> wrkWorkIpShareByYear = wrkWorkIpShareService.getWrkWorkIpShareByTypeAndRole(wrkWork.getWorkId(), wrkWork.getWorkSocietyCode());
        if(CollectionUtils.isEmpty(wrkWorkIpShareByYear)){
            return;
        }

        for(ListMatchDataOverseasMapping listMatchDataOverseasMapping : listMatchDataOverseasMappingList){
            listMatchDataOverseasMapping.setMatchWorkId(wrkWork.getWorkId());
            listMatchDataOverseasMapping.setMatchWorkSocietyCode(wrkWork.getWorkSocietyCode());
            listMatchDataOverseasMapping.setMatchWorkUniqueKey(workTitle.getWorkUniqueKey());
            listMatchDataOverseasMapping.setMatchWorkTitle(workTitle.getTitleEn());
            Integer status = getStatus(listMatchDataOverseasMapping, wrkWorkIpShareByYear);
            listMatchDataOverseasMapping.setStatus(status);
            if(status == Constants.OVERSEA_IP_STATUS_0){
                listMatchDataOverseasMapping.setMatchIpName(null);
                listMatchDataOverseasMapping.setMatchIpSoc(null);
                listMatchDataOverseasMapping.setMatchIpNameNo(null);
                listMatchDataOverseasMapping.setMatchIpShare(null);
                listMatchDataOverseasMapping.setMatchPaNameNo(null);
                listMatchDataOverseasMapping.setManual(null);
                listMatchDataOverseasMapping.setMatchNameType(null);
                listMatchDataOverseasMapping.setMatchIpType(null);
                listMatchDataOverseasMapping.setMatchIpRole(null);
            }
        }
    }

    @Override
    public void checkAndSetStatusAuto(List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList) {
        if(CollectionUtils.isEmpty(listMatchDataOverseasMappingList)){
            return;
        }

        ListMatchDataOverseasMapping mapping = listMatchDataOverseasMappingList.get(0);
        Long workId = mapping.getMatchWorkId();
        Integer soc = mapping.getMatchWorkSocietyCode();

        List<WrkWorkIpShare> wrkWorkIpShareByYear = wrkWorkIpShareService.getWrkWorkIpShareByTypeAndRole(workId, soc);
        if(CollectionUtils.isEmpty(wrkWorkIpShareByYear)){
            return;
        }

        for(ListMatchDataOverseasMapping listMatchDataOverseasMapping : listMatchDataOverseasMappingList){
            Integer status = getStatus(listMatchDataOverseasMapping, wrkWorkIpShareByYear);
            listMatchDataOverseasMapping.setStatus(status);
        }
    }

    @Override
    public List<WrkWorkIpShare> checkAndSetStatus(ListMatchDataOverseasMapping listMatchDataOverseasMapping) {
        Long workId = listMatchDataOverseasMapping.getMatchWorkId();
        Integer soc = listMatchDataOverseasMapping.getMatchWorkSocietyCode();

        WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkId(workId, soc);
        if(wrkWork == null){
            return null;
        }
        List<WrkWorkIpShare> wrkWorkIpShareByYear = wrkWorkIpShareService.getWrkWorkIpShareByTypeAndRole(workId, soc);

        /*if(CollectionUtils.isNotEmpty(wrkWorkIpShareByYear)){
            for(WrkWorkIpShare wrkWorkIpShare : wrkWorkIpShareByYear){
                if(StringUtils.isNotBlank(wrkWorkIpShare.getName())){
                    wrkWorkIpShare.setName(wrkWorkIpShare.getName().replaceAll(" ", ""));
                } else if(StringUtils.isNotBlank(wrkWorkIpShare.getDummyNameRoman())){
                    wrkWorkIpShare.setName(wrkWorkIpShare.getDummyNameRoman().replaceAll(" ", ""));
                }

                if(StringUtils.isNotBlank(wrkWorkIpShare.getChineseName())){
                    wrkWorkIpShare.setChineseName(CommonUtil.ChineseNormalizer.doNormalize(wrkWorkIpShare.getChineseName().replaceAll(" ", "")));
                }

            }
        }*/

        //name no一致，ip soc 為非161會員時，自動不匹配，不匹配原因為NON MUST IP
        //name no一致，ip soc為161時，自動匹配通過，
        //name no不一致，需要人工來審核
        Integer status = getStatus(listMatchDataOverseasMapping, wrkWorkIpShareByYear);
        listMatchDataOverseasMapping.setStatus(status);

        return wrkWorkIpShareByYear;
    }

    private Integer getStatus(ListMatchDataOverseasMapping listMatchDataOverseasMapping, List<WrkWorkIpShare> wrkWorkIpShareByYear) {
        if (CollectionUtils.isEmpty(wrkWorkIpShareByYear)) {
            return Constants.OVERSEA_IP_STATUS_0;
        }
        String ipNameNo = listMatchDataOverseasMapping.getIpNameNo();
        if(StringUtils.isBlank(ipNameNo)){
            return Constants.OVERSEA_IP_STATUS_0;
        }

        String remitIpRole = listMatchDataOverseasMapping.getWorkIpRole();
        if(StringUtils.isBlank(remitIpRole)){
            return Constants.OVERSEA_IP_STATUS_0;
        }
        if(StringUtils.containsAny(listMatchDataOverseasMapping.getIpName().toUpperCase(),"UNKNOWN","UNIDENTIFIED","NON-MEMBER","WARSAW RULE")){
            WrkWorkIpShare wrkWorkIpShare = wrkWorkIpShareByYear.get(0);
            if(WORK_IP_ROLE_CRD_SE.contains(remitIpRole)){
                listMatchDataOverseasMapping.setMatchIpNameNo("00288936892");
                listMatchDataOverseasMapping.setMatchIpType("L");
                listMatchDataOverseasMapping.setMatchIpName("UNKNOWN PUBLISHER");
            } else {
                listMatchDataOverseasMapping.setMatchIpNameNo("00288936400");
                listMatchDataOverseasMapping.setMatchIpType("N");
                listMatchDataOverseasMapping.setMatchIpName("UNKNOWN COMPOSER AUTHOR");
            }
            listMatchDataOverseasMapping.setMatchWorkSocietyCode(wrkWorkIpShare.getWorkSocietyCode());
            listMatchDataOverseasMapping.setMatchWorkId(wrkWorkIpShare.getWorkId());
            listMatchDataOverseasMapping.setMatchWorkUniqueKey(wrkWorkIpShare.getWorkUniqueKey());
            listMatchDataOverseasMapping.setMatchNameType("DF");
            listMatchDataOverseasMapping.setMatchIpSoc(99);
            listMatchDataOverseasMapping.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
            listMatchDataOverseasMapping.setMatchIpRole(listMatchDataOverseasMapping.getWorkIpRole());
            listMatchDataOverseasMapping.setRejectCode("F02");
            listMatchDataOverseasMapping.setRejectMessage("NON MUST MEMBER");
            listMatchDataOverseasMapping.setManual(false);
            return OVERSEA_IP_STATUS_1;
        }

        List<WrkWorkIpShare> ipNameNoIpshares = wrkWorkIpShareByYear.stream().filter(wrkWorkIpShare -> StringUtils.equals(ipNameNo, wrkWorkIpShare.getIpNameNo())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(ipNameNoIpshares)){
            /*if( StringUtils.isNotBlank(remitIpRole) && Constants.WORK_IP_ROLE_COMPANY.contains(remitIpRole)){
                List<MbrIpStatus> mbrIpStatusList = mbrIpStatusService.getMbrIpStatusByIpNameNoValid(ipNameNo);
                if(CollectionUtils.isNotEmpty(mbrIpStatusList)){
                    MbrIpStatus mbrIpStatus = mbrIpStatusList.get(0);
                    if(mbrIpStatus.getStatusCode() == 3){
                        return OVERSEA_IP_STATUS_0;
                    }
                }
            }*/
            WrkWorkIpShare wrkWorkIpShare = ipNameNoIpshares.get(0);
            listMatchDataOverseasMapping.setMatchIpNameNo(wrkWorkIpShare.getIpNameNo());
            listMatchDataOverseasMapping.setMatchIpName(StringUtils.isEmpty(wrkWorkIpShare.getChineseName()) ? wrkWorkIpShare.getName() : wrkWorkIpShare.getChineseName());
            listMatchDataOverseasMapping.setMatchIpType(wrkWorkIpShare.getIpType());
            listMatchDataOverseasMapping.setMatchWorkSocietyCode(wrkWorkIpShare.getWorkSocietyCode());
            listMatchDataOverseasMapping.setMatchWorkId(wrkWorkIpShare.getWorkId());
            listMatchDataOverseasMapping.setMatchIpRole(wrkWorkIpShare.getWorkIpRole());
            listMatchDataOverseasMapping.setMatchWorkUniqueKey(wrkWorkIpShare.getWorkUniqueKey());
            listMatchDataOverseasMapping.setManual(false);
            String paNameNo = paNameNoMap.get(wrkWorkIpShare.getIpBaseNo());
            if(paNameNo == null){
                MbrIpName mbrIpName = mbrIpNameService.getPANameByIpBaseNo(wrkWorkIpShare.getIpBaseNo());
                if(mbrIpName != null){
                    paNameNo = mbrIpName.getIpNameNo();
                    paNameNoMap.put(wrkWorkIpShare.getIpBaseNo(), mbrIpName.getIpNameNo());
                }
            }

            if(StringUtils.isNotBlank(paNameNo)){
                listMatchDataOverseasMapping.setMatchPaNameNo(paNameNo);
            }
            listMatchDataOverseasMapping.setMatchIpShare(ipNameNoIpshares.stream().filter(i->i.getIpShare()!=null && i.getIpShare().compareTo(BigDecimal.ZERO) == 1).map(WrkWorkIpShare::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add));
            if (listMatchDataOverseasMapping.getIpSocietyCode() != null) {
                listMatchDataOverseasMapping.setMatchIpSoc(listMatchDataOverseasMapping.getIpSocietyCode());
                if (!listMatchDataOverseasMapping.getIpSocietyCode().equals(161)) {
                    listMatchDataOverseasMapping.setRejectCode("F02");
                    listMatchDataOverseasMapping.setRejectMessage("NON MUST MEMBER");
                    return OVERSEA_IP_STATUS_1;
                }
            }
            return OVERSEA_IP_STATUS_1;
        }

        if( Constants.WORK_IP_ROLE_CRD_SE.contains(remitIpRole)){
                /*if(StringUtils.equals(ipNameNo,"00000000000")){
                    return OVERSEA_IP_STATUS_0;
                }*/
            if(!StringUtils.equals(ipNameNo,"00000000000")){
                MbrIpName mbrIpName = mbrIpNameService.getIpNameByIpNameNo(ipNameNo);
                if(mbrIpName == null){
                    return OVERSEA_IP_STATUS_0;
                }
                List<MbrIpStatus> mbrIpStatusList = mbrIpStatusService.getMbrIpStatusByIpNameNoValid(ipNameNo);
                if(CollectionUtils.isNotEmpty(mbrIpStatusList)){
                    MbrIpStatus mbrIpStatus = mbrIpStatusList.get(0);
                    String ipBaseNo = mbrIpStatus.getIpBaseNo();
                    if(mbrIpStatus.getStatusCode() != 3){
                        WrkWorkIpShare wrkWorkIpShare = wrkWorkIpShareByYear.get(0);
                        listMatchDataOverseasMapping.setMatchIpNameNo(ipNameNo);
                        listMatchDataOverseasMapping.setMatchIpName(StringUtils.isBlank(mbrIpName.getChineseName() ) ? mbrIpName.getName() : mbrIpName.getChineseName());
//                        listMatchDataOverseasMapping.setMatchIpType(listMatchDataOverseasMapping.getMatchNameType());
                        listMatchDataOverseasMapping.setMatchIpRole(listMatchDataOverseasMapping.getWorkIpRole());
                        listMatchDataOverseasMapping.setMatchWorkSocietyCode(wrkWorkIpShare.getWorkSocietyCode());
                        listMatchDataOverseasMapping.setMatchWorkId(wrkWorkIpShare.getWorkId());
                        listMatchDataOverseasMapping.setMatchWorkUniqueKey(wrkWorkIpShare.getWorkUniqueKey());
                        listMatchDataOverseasMapping.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
                        listMatchDataOverseasMapping.setMatchIpType("L");
                        listMatchDataOverseasMapping.setManual(false);
//                        MbrIpName mbrIpName = mbrIpNameService.getPANameByIpBaseNo(mbrIpStatus.getIpBaseNo());
                        String paNameNo = paNameNoMap.get(ipBaseNo);
                        if(paNameNo == null){
                            MbrIpName paMbrIpName = mbrIpNameService.getPANameByIpBaseNo(ipBaseNo);
                            if(mbrIpName != null){
                                paNameNo = paMbrIpName.getIpNameNo();
                                paNameNoMap.put(ipBaseNo, paMbrIpName.getIpNameNo());
                            }
                        }

                        if(StringUtils.isNotBlank(paNameNo)){
                            listMatchDataOverseasMapping.setMatchPaNameNo(paNameNo);
                            listMatchDataOverseasMapping.setMatchNameType("PA");
                        }

                        if (listMatchDataOverseasMapping.getIpSocietyCode() != null) {
                            listMatchDataOverseasMapping.setMatchIpSoc(listMatchDataOverseasMapping.getIpSocietyCode());
                            if (!listMatchDataOverseasMapping.getIpSocietyCode().equals(161)) {
                                listMatchDataOverseasMapping.setRejectCode("F02");
                                listMatchDataOverseasMapping.setRejectMessage("NON MUST MEMBER");
                                return OVERSEA_IP_STATUS_1;
                            }
                        }

                        return OVERSEA_IP_STATUS_1;
                    }
                } else {
                    return OVERSEA_IP_STATUS_0;
                }
            }
        } else

        if(WORK_IP_ROLE_CRD_CA.contains(remitIpRole)){
//                wrkWorkIpShareByYear = wrkWorkIpShareByYear.stream().filter(wrkWorkIpShare -> WORK_IP_ROLE_CRD_CA.contains(wrkWorkIpShare.)).collect(Collectors.toList());

            String ipName = listMatchDataOverseasMapping.getIpName();
            String resortedMappingName = ipName;
            if(StringUtils.isNotBlank(ipName)){
//                ipName = ipName.replaceAll(" ","");
                resortedMappingName = this.resortedComposer(ipName);
            }

            String finalIpName = resortedMappingName;
            ipNameNoIpshares = wrkWorkIpShareByYear.stream().filter(wrkWorkIpShare ->ipNameMatch(finalIpName, wrkWorkIpShare)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(ipNameNoIpshares)){
                Set<String> ipBaseNos = new HashSet<>();
                for(WrkWorkIpShare wrkWorkIpShare : wrkWorkIpShareByYear){
                        /*if(StringUtils.isBlank(wrkWorkIpShare.getIpNameNo())){
                            continue;
                        }

                        if(ipBaseNos.contains(wrkWorkIpShare.getIpNameNo())){
                            continue;
                        }

                        ipBaseNos.add(wrkWorkIpShare.getIpNameNo());

                        MbrIpNameMerge mbrIpNameMerge = mbrIpNameMergeService.getIpNameMergeByNameNo(wrkWorkIpShare.getIpNameNo());

                        if(mbrIpNameMerge == null){
                            continue;
                        }*/

                    if(StringUtils.isBlank(wrkWorkIpShare.getIpBaseNo())){
                        continue;
                    }

                    if(ipBaseNos.contains(wrkWorkIpShare.getIpBaseNo())){
                        continue;
                    }

                    ipBaseNos.add(wrkWorkIpShare.getIpBaseNo());

                    List<IpNameVO> allIpNameNos = mbrIpNameService.getIpNameListByBaseNo(wrkWorkIpShare.getIpBaseNo());
                    if(CollectionUtils.isEmpty(allIpNameNos)){
                        continue;
                    }

                    for(IpNameVO ipNameVO : allIpNameNos){
                        if(StringUtils.equals(ipNameNo,ipNameVO.getNameNo())){
                            ipNameNoIpshares.add(wrkWorkIpShare);
                            break;
                        }

                        if(StringUtils.isNotBlank(ipNameVO.getName())){
                            String workResortedName =  resortedComposer(ipNameVO.getName());
                            if(StringUtils.equals(finalIpName,workResortedName)){
                                ipNameNoIpshares.add(wrkWorkIpShare);
                                break;
                            }
                        }

                        if(StringUtils.isNotBlank(ipNameVO.getChineseRomaName())){
                            String workResortedName =  resortedComposer(ipNameVO.getChineseRomaName());
                            if(StringUtils.equals(finalIpName,workResortedName)){
                                ipNameNoIpshares.add(wrkWorkIpShare);
                                break;
                            }
                        }
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(ipNameNoIpshares)){
                WrkWorkIpShare wrkWorkIpShare = ipNameNoIpshares.get(0);
                listMatchDataOverseasMapping.setMatchIpNameNo(wrkWorkIpShare.getIpNameNo());
                listMatchDataOverseasMapping.setMatchIpName(StringUtils.isEmpty(wrkWorkIpShare.getChineseName()) ? wrkWorkIpShare.getName() : wrkWorkIpShare.getChineseName());
                listMatchDataOverseasMapping.setMatchIpType(wrkWorkIpShare.getIpType());
                listMatchDataOverseasMapping.setMatchWorkSocietyCode(wrkWorkIpShare.getWorkSocietyCode());
                listMatchDataOverseasMapping.setMatchWorkId(wrkWorkIpShare.getWorkId());
                listMatchDataOverseasMapping.setMatchIpRole(wrkWorkIpShare.getWorkIpRole());
                listMatchDataOverseasMapping.setMatchIpType(wrkWorkIpShare.getIpType());
                listMatchDataOverseasMapping.setManual(false);
                String paNameNo = paNameNoMap.get(wrkWorkIpShare.getIpBaseNo());
                if(paNameNo == null){
                    MbrIpName mbrIpName = mbrIpNameService.getPANameByIpBaseNo(wrkWorkIpShare.getIpBaseNo());
                    if(mbrIpName != null){
                        paNameNo = mbrIpName.getIpNameNo();
                        paNameNoMap.put(wrkWorkIpShare.getIpBaseNo(), mbrIpName.getIpNameNo());
                    }
                }

                if(StringUtils.isNotBlank(paNameNo)){
                    listMatchDataOverseasMapping.setMatchPaNameNo(paNameNo);
                }
                listMatchDataOverseasMapping.setMatchIpShare(ipNameNoIpshares.stream().filter(i->i.getIpShare()!=null && i.getIpShare().compareTo(BigDecimal.ZERO) == 1).map(WrkWorkIpShare::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add));
                if (listMatchDataOverseasMapping.getIpSocietyCode() != null) {
                    listMatchDataOverseasMapping.setMatchIpSoc(listMatchDataOverseasMapping.getIpSocietyCode());
                    if (!listMatchDataOverseasMapping.getIpSocietyCode().equals(161)) {
                        listMatchDataOverseasMapping.setRejectCode("F02");
                        listMatchDataOverseasMapping.setRejectMessage("NON MUST MEMBER");
                        return OVERSEA_IP_STATUS_1;
                    }
                }
                return OVERSEA_IP_STATUS_1;
            }
        }

        return Constants.OVERSEA_IP_STATUS_0;
    }

    @Override
    public List<ListMatchDataOverseasMapping> listRemittedIp(Long fId, String dataUniqueKey) {
        ListMatchDataOverseasMapping listMatchDataOverseasMapping = new ListMatchDataOverseasMapping();
        listMatchDataOverseasMapping.setDataUniqueKey(dataUniqueKey);
        listMatchDataOverseasMapping.setFileBaseId(fId);
        return listMatchDataOverseasMappingMapper.select(listMatchDataOverseasMapping);
    }

    @Override
    @Transactional
    public void checkIp(String workUniqueKey, List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList) {
        if (CollectionUtils.isEmpty(listMatchDataOverseasMappingList)) {
            return;
        }

        listMatchDataOverseasMappingList = listMatchDataOverseasMappingList.stream().filter(l -> l.getMatchFlag()).distinct().collect(Collectors.toList());
        List<ListMatchDataOverseasMapping> allMatchedList = new ArrayList<>();

        for(ListMatchDataOverseasMapping listMatchDataOverseasMapping : listMatchDataOverseasMappingList){

            //更新当前修改数据
            //http://*********:8080/browse/MUST-3897
            if (listMatchDataOverseasMapping.getStatus() == OVERSEA_IP_STATUS_1 && listMatchDataOverseasMapping.getMatchIpSoc() != null) {
                if(listMatchDataOverseasMapping.getMatchIpSoc().equals(161)){
                    listMatchDataOverseasMapping.setRejectCode(null);
                    listMatchDataOverseasMapping.setRejectMessage(null);
                } else {
                    listMatchDataOverseasMapping.setRejectCode("F02");
                    listMatchDataOverseasMapping.setRejectMessage("NON MUST MEMBER");
                }
            } else if(StringUtils.equals(listMatchDataOverseasMapping.getRejectCode(),"")){
                listMatchDataOverseasMapping.setRejectCode(null);
                listMatchDataOverseasMapping.setRejectMessage(null);
            }

            listMatchDataOverseasMapping.init();
            listMatchDataOverseasMapping.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
            listMatchDataOverseasMapping.setManual(true);
            if(listMatchDataOverseasMapping.getMatchIpType().equals("PA")){
                listMatchDataOverseasMapping.setMatchPaNameNo(listMatchDataOverseasMapping.getIpNameNo());
            } else {
                MbrIpName paMbrIpName = mbrIpNameService.getPANameByIpNameNo(listMatchDataOverseasMapping.getIpNameNo());
                if(paMbrIpName != null){
                    listMatchDataOverseasMapping.setMatchPaNameNo(paMbrIpName.getIpNameNo());
                }
            }
            allMatchedList.add(listMatchDataOverseasMapping);

            // 自动匹配同matchWorkUniqueKey+ipNameNo
            List<ListMatchDataOverseasMapping> list = this.getByMatchWorkUniqueKeyAndIpName(listMatchDataOverseasMapping.getMatchWorkUniqueKey(), listMatchDataOverseasMapping.getIpName());
            for (ListMatchDataOverseasMapping listMatchDataOverseasMapping1 : list){
                if(listMatchDataOverseasMapping.getId().equals(listMatchDataOverseasMapping1.getId())){
                    continue;
                } else {
                    listMatchDataOverseasMapping1.setMatchIpNameNo(listMatchDataOverseasMapping.getMatchIpNameNo());
                    listMatchDataOverseasMapping1.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
                    listMatchDataOverseasMapping1.setMatchIpName(listMatchDataOverseasMapping.getMatchIpName());
                    listMatchDataOverseasMapping1.setMatchIpType(listMatchDataOverseasMapping.getMatchIpType());
                    listMatchDataOverseasMapping1.setMatchIpRole(listMatchDataOverseasMapping.getMatchIpRole());
                    listMatchDataOverseasMapping1.setMatchIpSoc(listMatchDataOverseasMapping.getMatchIpSoc());
                    listMatchDataOverseasMapping1.setMatchPaNameNo(listMatchDataOverseasMapping.getMatchPaNameNo());
                    listMatchDataOverseasMapping1.setStatus(listMatchDataOverseasMapping.getStatus());
                    listMatchDataOverseasMapping1.setManual(true);
                    listMatchDataOverseasMapping1.setAmendTime(new Date());
                    listMatchDataOverseasMapping1.setRejectCode(listMatchDataOverseasMapping.getRejectCode());
                    listMatchDataOverseasMapping1.setRejectMessage(listMatchDataOverseasMapping.getRejectMessage());

                    allMatchedList.add(listMatchDataOverseasMapping1);
                }
            }

        }

        listMatchDataOverseasMappingMapper.updateBatchByPrimaryKey(allMatchedList);

        Map<Long,List<ListMatchDataOverseasMapping>> map = allMatchedList.stream().filter(l -> l.getOverseasId() != null).collect(Collectors.groupingBy(ListMatchDataOverseasMapping::getOverseasId));
//        List<ListMatchDataOverseas> updateListMatchDataOverseasList = new ArrayList<>();
        for(Map.Entry<Long,List<ListMatchDataOverseasMapping>> entry : map.entrySet()){
            ListMatchDataOverseasMapping listMatchDataOverseasMapping = entry.getValue().get(0);
            ListMatchDataOverseas listMatchDataOverseas = new ListMatchDataOverseas();
            listMatchDataOverseas.setDataUniqueKey(listMatchDataOverseasMapping.getDataUniqueKey());
            listMatchDataOverseas.setFileBaseId(listMatchDataOverseasMapping.getFileBaseId());
            Boolean checkAll = ipCheckAll(listMatchDataOverseas);
            if(checkAll){
                ListMatchDataOverseas update = new ListMatchDataOverseas();
                listMatchDataOverseas.setId(entry.getKey());
                listMatchDataOverseas.setAmendTime(new Date());
                listMatchDataOverseas.setStatus(OVERSEA__STATUS_3);
                listMatchDataOverseasMapper.updateByPrimaryKeySelective(update);
            }
        }


        Set<String> dataUniqueKeySet = allMatchedList.stream().map(ListMatchDataOverseasMapping::getDataUniqueKey).collect(Collectors.toSet());
        for(String dataUniqueKey : dataUniqueKeySet){
            Boolean checkAll = ipCheckAllByDataUniqueKey(dataUniqueKey);
            if(checkAll){
                ListMatchDataOverseasUniq listMatchDataOverseasUniq = new ListMatchDataOverseasUniq();
                listMatchDataOverseasUniq.setStatus(OVERSEA__STATUS_3);
                listMatchDataOverseasUniq.setAmendTime(new Date());

                Example example = new Example(ListMatchDataOverseasUniq.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("dataUniqueKey",dataUniqueKey);
                listMatchDataOverseasUniqMapper.updateByExampleSelective(listMatchDataOverseasUniq,example);
            }
        }

        Set<Long> fileBaseIds = allMatchedList.stream().map(ListMatchDataOverseasMapping::getFileBaseId).collect(Collectors.toSet());
        this.setMatchAndUnmatch(fileBaseIds);
        /*
        Boolean hasIpUnCheck = listMatchDataOverseasMatchWorkCheckMapper.checkIPWhetherReviewedOfAll(workUniqueKey);
        if (!hasIpUnCheck) {
            ListMatchDataOverseasMatchWorkCheck listMatchDataOverseasMatchWorkCheck = new ListMatchDataOverseasMatchWorkCheck();
            listMatchDataOverseasMatchWorkCheck.setStatus(1);
            listMatchDataOverseasMatchWorkCheck.init();
            Example example = new Example(ListMatchDataOverseasMatchWorkCheck.class);
            Criteria criteria = example.createCriteria();
            criteria.andEqualTo("workUniqueKey", workUniqueKey);
            listMatchDataOverseasMatchWorkCheckMapper.updateByExampleSelective(listMatchDataOverseasMatchWorkCheck, example);
        }*/
    }

    public List<ListMatchDataOverseasMapping> checkByMatchWorkAndRemitIp(ListMatchDataOverseasMapping listMatchDataOverseasMapping){

        List<ListMatchDataOverseasMapping> resultList = new ArrayList<>();
        // 自动匹配同matchWorkUniqueKey+remitIp
        List<ListMatchDataOverseasMapping> list = this.getByMatchWorkUniqueKeyAndIpName(listMatchDataOverseasMapping.getMatchWorkUniqueKey(), listMatchDataOverseasMapping.getIpName());
        for (ListMatchDataOverseasMapping listMatchDataOverseasMapping1 : list){
            if(listMatchDataOverseasMapping.getId().equals(listMatchDataOverseasMapping1.getId())){
                continue;
            } else {
                listMatchDataOverseasMapping1.setMatchIpNameNo(listMatchDataOverseasMapping.getMatchIpNameNo());
                listMatchDataOverseasMapping1.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
                listMatchDataOverseasMapping1.setMatchIpName(listMatchDataOverseasMapping.getMatchIpName());
                listMatchDataOverseasMapping1.setMatchIpType(listMatchDataOverseasMapping.getMatchIpType());
                listMatchDataOverseasMapping1.setMatchIpRole(listMatchDataOverseasMapping.getMatchIpRole());
                listMatchDataOverseasMapping1.setMatchIpSoc(listMatchDataOverseasMapping.getMatchIpSoc());
                listMatchDataOverseasMapping1.setMatchPaNameNo(listMatchDataOverseasMapping.getMatchPaNameNo());
                listMatchDataOverseasMapping1.setStatus(listMatchDataOverseasMapping.getStatus());
                listMatchDataOverseasMapping1.setManual(true);
                listMatchDataOverseasMapping1.setAmendTime(new Date());
                listMatchDataOverseasMapping1.setRejectCode(listMatchDataOverseasMapping.getRejectCode());
                listMatchDataOverseasMapping1.setRejectMessage(listMatchDataOverseasMapping.getRejectMessage());
                resultList.add(listMatchDataOverseasMapping1);
            }
        }

        return resultList;
    }

    @Override
    @Transactional
    public String importAuditResults(Long id, String ipNameNo,String workIpRole) {

        ListMatchDataOverseasMapping listMatchDataOverseasMapping = this.getById(id);

        if(listMatchDataOverseasMapping == null){
            return "list_match_data_overseas_mapping表中不存在ID：" + id;
        }

        if(StringUtils.isBlank(ipNameNo)){
            return "match distributed IP name No.为空";
        }

        if(StringUtils.isBlank(workIpRole)){
            return "match distributed IP Role为空";
        }

        if(!ALL_WORK_IP_ROLE.contains(workIpRole)){
            return "match distributed IP Role 不存在";
        }

        MbrIpName mbrIpName = mbrIpNameService.getIpNameByIpNameNo(ipNameNo);
        if(mbrIpName == null){
            return "ipNameNo { " + ipNameNo + " } 不存在";
        }
        MbrIpName mbrIpNamePa = mbrIpNameService.getPANameByIpNameNo(ipNameNo);
        ListMatchDataOverseas listMatchDataOverseas = listMatchDataOverseasMapper.selectByPrimaryKey(listMatchDataOverseasMapping.getOverseasId());

        if(listMatchDataOverseas == null || listMatchDataOverseas.getStatus() == 0){
            return "不存在匹配的作品信息";
        }

        listMatchDataOverseasMapping.setMatchPaNameNo(mbrIpNamePa.getIpNameNo());
        listMatchDataOverseasMapping.setMatchIpNameNo(mbrIpName.getIpNameNo());
        listMatchDataOverseasMapping.setMatchIpName(StringUtils.isBlank(mbrIpName.getChineseName()) ? mbrIpName.getName() : mbrIpName.getChineseName());
        listMatchDataOverseasMapping.setMatchIpType(mbrIpName.getNameType());
        listMatchDataOverseasMapping.setMatchIpRole(workIpRole);
        listMatchDataOverseasMapping.setMatchIpSoc(161);
        listMatchDataOverseasMapping.setManual(true);

        listMatchDataOverseasMapping.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
        listMatchDataOverseasMapping.setStatus(OVERSEA_IP_STATUS_1);
        listMatchDataOverseasMapping.setAmendTime(new Date());

        listMatchDataOverseasMappingMapper.updateByPrimaryKeySelective(listMatchDataOverseasMapping);

        Boolean checkAll = this.ipCheckAll(listMatchDataOverseas);
        if (checkAll) {
            listMatchDataOverseas.setStatus(OVERSEA__STATUS_3);
            listMatchDataOverseas.setAmendTime(new Date());
            listMatchDataOverseasMapper.updateByPrimaryKeySelective(listMatchDataOverseas);
        }


        checkAll = this.ipCheckAllByDataUniqueKey(listMatchDataOverseasMapping.getDataUniqueKey());
        if (checkAll) {
            Example example = new Example(ListMatchDataOverseasUniq.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("dataUniqueKey",listMatchDataOverseasMapping.getDataUniqueKey());

            ListMatchDataOverseasUniq listMatchDataOverseasUniq = new ListMatchDataOverseasUniq();
            listMatchDataOverseasUniq.setStatus(OVERSEA__STATUS_3);
            listMatchDataOverseasUniq.setAmendTime(new Date());
            listMatchDataOverseasUniqMapper.updateByExampleSelective(listMatchDataOverseasUniq,example);
        }

        return "";

    }

    @Override
    @Transactional
    public List<String> importAuditResults(Map<String,List<String>> map){
        List<String> results = new ArrayList<>();
        Set<Long> fileBaseIds = new HashSet<>();
        Set<Long> overseaIds = new HashSet<>();
        Set<String> dataUniqueKeys = new HashSet<>();
        for(Map.Entry<String,List<String>> entry : map.entrySet()){

            String message = "";
            List<String> list = entry.getValue();
            Long id = Long.valueOf(entry.getKey());
            String ipNameNo = list.get(0);
            String workIpRole = list.get(1);

            ListMatchDataOverseasMapping listMatchDataOverseasMapping = this.getById(id);

            if(listMatchDataOverseasMapping == null){
                message = "list_match_data_overseas_mapping表中不存在ID：" + id;
                results.add(message);
                continue;
            }

            if(StringUtils.isBlank(ipNameNo)){
                message = "match distributed IP name No.为空";
                results.add(message);
                continue;
            }

            if(StringUtils.isBlank(workIpRole)){
                message = "match distributed IP Role为空";
                results.add(message);
                continue;
            }

            if(!ALL_WORK_IP_ROLE.contains(workIpRole)){
                message = "match distributed IP Role 不存在";
                results.add(message);
                continue;
            }

            MbrIpName mbrIpName = mbrIpNameService.getIpNameByIpNameNo(ipNameNo);
            if(mbrIpName == null){
                message = "ipNameNo { " + ipNameNo + " } 不存在";
                results.add(message);
                continue;
            }
            MbrIpName mbrIpNamePa = mbrIpNameService.getPANameByIpNameNo(ipNameNo);
            ListMatchDataOverseas listMatchDataOverseas = listMatchDataOverseasMapper.selectByPrimaryKey(listMatchDataOverseasMapping.getOverseasId());

            if(listMatchDataOverseas == null || listMatchDataOverseas.getStatus() == 0){
                message = "不存在匹配的作品信息";
                results.add(message);
                continue;
            }

            listMatchDataOverseasMapping.setMatchPaNameNo(mbrIpNamePa.getIpNameNo());
            listMatchDataOverseasMapping.setMatchIpNameNo(mbrIpName.getIpNameNo());
            listMatchDataOverseasMapping.setMatchIpName(StringUtils.isBlank(mbrIpName.getChineseName()) ? mbrIpName.getName() : mbrIpName.getChineseName());
            listMatchDataOverseasMapping.setMatchIpType(mbrIpName.getNameType());
            listMatchDataOverseasMapping.setMatchIpRole(workIpRole);
//            listMatchDataOverseasMapping.setMatchIpSoc(161);
            listMatchDataOverseasMapping.setManual(true);

            listMatchDataOverseasMapping.setMatchIpShare(listMatchDataOverseasMapping.getShareRatio());
            listMatchDataOverseasMapping.setStatus(OVERSEA_IP_STATUS_1);
            listMatchDataOverseasMapping.setAmendTime(new Date());

            List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList = checkByMatchWorkAndRemitIp(listMatchDataOverseasMapping);
            listMatchDataOverseasMappingList.add(listMatchDataOverseasMapping);

            listMatchDataOverseasMappingMapper.updateBatchByPrimaryKey(listMatchDataOverseasMappingList);

            /*Boolean checkAll = this.ipCheckAll(listMatchDataOverseas);
            if (checkAll) {
                listMatchDataOverseas.setStatus(OVERSEA__STATUS_3);
                listMatchDataOverseas.setAmendTime(new Date());
                listMatchDataOverseasMapper.updateByPrimaryKey(listMatchDataOverseas);
            }


            Integer status = listMatchDataOverseasMapper.getStatusByDataUniqueKey(listMatchDataOverseasMapping.getDataUniqueKey());
            if (status == OVERSEA__STATUS_3) {
                Example example = new Example(ListMatchDataOverseasUniq.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("dataUniqueKey",listMatchDataOverseasMapping.getDataUniqueKey());

                ListMatchDataOverseasUniq listMatchDataOverseasUniq = new ListMatchDataOverseasUniq();
                listMatchDataOverseasUniq.setStatus(OVERSEA__STATUS_3);
                listMatchDataOverseasUniq.setAmendTime(new Date());
                listMatchDataOverseasUniqMapper.updateByExampleSelective(listMatchDataOverseasUniq,example);
            }*/

            overseaIds.addAll(listMatchDataOverseasMappingList.stream().map(ListMatchDataOverseasMapping::getOverseasId).collect(Collectors.toList()));
            dataUniqueKeys.addAll(listMatchDataOverseasMappingList.stream().map(ListMatchDataOverseasMapping::getDataUniqueKey).collect(Collectors.toList()));
            fileBaseIds.addAll(listMatchDataOverseasMappingList.stream().map(ListMatchDataOverseasMapping::getFileBaseId).collect(Collectors.toList()));
            results.add(message);
        }

        for(Long overseaId : overseaIds){
            Boolean checkAll = this.ipCheckAll(overseaId);
            if (checkAll) {
                ListMatchDataOverseas listMatchDataOverseas = new ListMatchDataOverseas();
                listMatchDataOverseas.setId(overseaId);
                listMatchDataOverseas.setStatus(OVERSEA__STATUS_3);
                listMatchDataOverseas.setAmendTime(new Date());
                listMatchDataOverseasMapper.updateByPrimaryKeySelective(listMatchDataOverseas);
            }
        }

        for(String dataUniqueKey : dataUniqueKeys){
            Integer status = listMatchDataOverseasMapper.getStatusByDataUniqueKey(dataUniqueKey);
            if(status == OVERSEA__STATUS_3){
                ListMatchDataOverseasUniq listMatchDataOverseasUniq = new ListMatchDataOverseasUniq();
                listMatchDataOverseasUniq.setStatus(OVERSEA__STATUS_3);
                listMatchDataOverseasUniq.setAmendTime(new Date());

                Example example = new Example(ListMatchDataOverseasUniq.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("dataUniqueKey",dataUniqueKey);
                listMatchDataOverseasUniqMapper.updateByExampleSelective(listMatchDataOverseasUniq,example);
            }
        }

        this.setMatchAndUnmatch(fileBaseIds);
        return results;
    }

    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingByDataUniqueKey(String dataUniqueKey) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataUniqueKey", dataUniqueKey);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

    @Override
    public Integer getCountByFidAndDataUniqueKey(Long fileBaseId, String dataUniqueKey) {

        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        if(fileBaseId != null){
            criteria.andEqualTo("fileBaseId", fileBaseId);
        }
        criteria.andEqualTo("dataUniqueKey", dataUniqueKey);
        return listMatchDataOverseasMappingMapper.selectCountByExample(example);
    }

    @Override
    public Integer updateByFileMappingId(ListMatchDataOverseasMapping listMatchDataOverseasMapping) {
        return listMatchDataOverseasMappingMapper.updateByFileMappingId(listMatchDataOverseasMapping);
    }

    @Override
    public Integer insertOnDuplicateKeyUpdate(List<ListMatchDataOverseasMapping> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        return listMatchDataOverseasMappingMapper.insertOnDuplicateKeyUpdate(list);
    }

    @Override
    public Map<Long, BigDecimal> getAmountGroupByFid(List<Long> fileBaseIds) {
        List<ListMatchDataOverseasMapping> list = listMatchDataOverseasMappingMapper.getListMatchDataOverseasMappingGroupByFid(fileBaseIds);
        Map<Long, BigDecimal> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach( l -> map.put(l.getFileBaseId(),l.getAmount()));
        }
        return map;

    }

    @Override
    @Transactional
    public void checkIpManual(String workUniqueKey, List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList) {

        listMatchDataOverseasMappingList.forEach(listMatchDataOverseasMapping -> {
            //http://*********:8080/browse/MUST-3897
            if (listMatchDataOverseasMapping.getStatus() == 1 && listMatchDataOverseasMapping.getMatchIpSoc() != null && !listMatchDataOverseasMapping.getMatchIpSoc().equals(161)) {
                listMatchDataOverseasMapping.setRejectCode("F02");
                listMatchDataOverseasMapping.setRejectMessage("NON MUST MEMBER");
            }
            listMatchDataOverseasMapping.setMatchWorkUniqueKey(workUniqueKey);
            listMatchDataOverseasMapping.init();
            listMatchDataOverseasMapping.setManual(true);
        });
        listMatchDataOverseasMappingMapper.updateBatchByPrimaryKeySelective(listMatchDataOverseasMappingList);

        Long overseasId = listMatchDataOverseasMappingList.get(0).getOverseasId();
//        List<ListMatchDataOverseasMapping> list = getListMatchDataOverseasMappingListByOverseasId(overseasId);
        List<ListMatchDataOverseasMapping> unCheckList = listMatchDataOverseasMappingList.stream().filter(l -> l.getStatus() == 0).collect(Collectors.toList());
        ListMatchDataOverseas listMatchDataOverseas = listMatchDataOverseasMapper.selectByPrimaryKey(overseasId);
        WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkUniqueKey(workUniqueKey);
        WrkWorkTitle wrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(workUniqueKey,"OT");
        if(!StringUtils.equals(listMatchDataOverseas.getMatchWorkUniqueKey(),workUniqueKey) || listMatchDataOverseas.getStatus() == 0){
            listMatchDataOverseas.setMatchWorkUniqueKey(workUniqueKey);
            listMatchDataOverseas.setMatchWorkgenre(wrkWorkTitle.getGenreCode());
            listMatchDataOverseas.setMatchWorkTitle(wrkWorkTitle.getTitleEn());
            listMatchDataOverseas.setMatchWorkId(wrkWork.getWorkId());
            listMatchDataOverseas.setMatchWorkSocietyCode(wrkWork.getWorkSocietyCode());
            listMatchDataOverseas.setMatchWorkTitleId(wrkWorkTitle.getId());
            listMatchDataOverseas.setMatchSubTitleId(wrkWorkTitle.getSubTitleId());
            listMatchDataOverseas.setMatchWorkType(wrkWork.getWorkType());
            listMatchDataOverseas.setMatchType("M");
            listMatchDataOverseas.setAmendTime(new Date());
        }
        if(CollectionUtils.isEmpty(unCheckList)){
            listMatchDataOverseas.setStatus(3);
        }

        listMatchDataOverseas.setAmendTime(new Date());
        listMatchDataOverseasMapper.updateByPrimaryKey(listMatchDataOverseas);

        String dataUniqueKey = listMatchDataOverseasMappingList.get(0).getDataUniqueKey();
        List<ListMatchDataOverseas> listMatchDataOverseasList = getListMatchDataOverseasByMd5(dataUniqueKey);
        Set<Integer> statusSet = listMatchDataOverseasList.stream().map(ListMatchDataOverseas :: getStatus).collect(Collectors.toSet());
        List<ListMatchDataOverseasUniq> listMatchDataOverseasUniqList = getListMatchDataOverseasUniqByMd5(dataUniqueKey);
        if(CollectionUtils.isNotEmpty(listMatchDataOverseasUniqList)){
            ListMatchDataOverseasUniq listMatchDataOverseasUniq = listMatchDataOverseasUniqList.get(0);
            if(!StringUtils.equals(listMatchDataOverseasUniq.getMatchWorkUniqueKey(),workUniqueKey) || listMatchDataOverseasUniq.getStatus() == 0){
                listMatchDataOverseasUniq.setMatchWorkUniqueKey(workUniqueKey);
                listMatchDataOverseasUniq.setMatchWorkTitle(wrkWorkTitle.getTitleEn());
                listMatchDataOverseasUniq.setMatchWorkId(wrkWork.getWorkId());
                listMatchDataOverseasUniq.setMatchWorkSocietyCode(wrkWork.getWorkSocietyCode());
                listMatchDataOverseasUniq.setMatchWorkTitleId(wrkWorkTitle.getId());
                listMatchDataOverseasUniq.setMatchSubTitleId(wrkWorkTitle.getSubTitleId());
                listMatchDataOverseasUniq.setMatchWorkType(wrkWork.getWorkType());
            }
            if(statusSet.contains(0)){
                listMatchDataOverseasUniq.setStatus(0);
            } else if(statusSet.contains(1)){
                listMatchDataOverseasUniq.setStatus(1);
            } else {
                listMatchDataOverseasUniq.setStatus(3);
            }
            listMatchDataOverseasUniq.setAmendTime(new Date());
            listMatchDataOverseasUniqMapper.updateByPrimaryKey(listMatchDataOverseasUniq);
        }

        Set<Long> fileBaseIds = listMatchDataOverseasMappingList.stream().map(l-> l.getFileBaseId()).collect(Collectors.toSet());
        setMatchAndUnmatch(fileBaseIds);

    }

    public List<ListMatchDataOverseasMapping> getByMatchWorkUniqueKeyAndIpName(String matchWorkUniqueKey,String remitIpName) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("matchWorkUniqueKey", matchWorkUniqueKey);
        criteria.andEqualTo("ipName", remitIpName);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

    @Override
    public List<ListMatchDataOverseasMapping> getUnCheckByDataUniqueKey(String dataUniqueKey) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataUniqueKey", dataUniqueKey);
//        criteria.andEqualTo("status", Constants.OVERSEA_IP_STATUS_0);
        return listMatchDataOverseasMappingMapper.selectByExample(example);
    }

    @Override
    public Boolean ipCheckAll(ListMatchDataOverseas listMatchDataOverseas) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("fileBaseId", listMatchDataOverseas.getFileBaseId());
        criteria.andEqualTo("dataUniqueKey", listMatchDataOverseas.getDataUniqueKey());
        criteria.andEqualTo("status", Constants.OVERSEA_IP_STATUS_0);
        int count = listMatchDataOverseasMappingMapper.selectCountByExample(example);
        return count == 0;
    }

    public Boolean ipCheckAll(Long overseasId) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("overseasId", overseasId);
        criteria.andEqualTo("status", Constants.OVERSEA_IP_STATUS_0);
        int count = listMatchDataOverseasMappingMapper.selectCountByExample(example);
        return count == 0;
    }

    @Override
    public Boolean ipCheckAllByDataUniqueKey(String dataUniqueKey) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataUniqueKey", dataUniqueKey);
        criteria.andEqualTo("status", Constants.OVERSEA_IP_STATUS_0);
        int count = listMatchDataOverseasMappingMapper.selectCountByExample(example);
        return count == 0;
    }

    @Override
    public List<String> getDataUniqueKeysWithDataUniqueKeys(List<String> checkedDataUniqueKeyList) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        example.selectProperties("dataUniqueKey");
        criteria.andIn("dataUniqueKey", checkedDataUniqueKeyList);
        example.setDistinct(true);
        List<ListMatchDataOverseasMapping> listMatchDataOverseasList = listMatchDataOverseasMappingMapper.selectByExample(example);

        return listMatchDataOverseasList.stream().map(ListMatchDataOverseasMapping::getDataUniqueKey).collect(Collectors.toList());
    }

    @Override
    public List<DistOverseasCheckingIpNotInWork> getListMatchDataOverseasMappingListByFidsAndRejectCode(List<Long> fids, String rejectCode) {

        if (fids == null || fids.isEmpty()) {
            return new ArrayList<>();
        }
        return listMatchDataOverseasMappingMapper.getListMatchDataOverseasMappingListByFidsAndRejectCode(fids,rejectCode);
    }

    @Override
    public List<DistOverseasCheckingIpNotInWork> getListMatchDataOverseasMappingListByFidsAndNotInWork(List<Long> fids, Integer notInWork) {

        if (fids == null || fids.isEmpty()) {
            return new ArrayList<>();
        }
        return listMatchDataOverseasMappingMapper.getListMatchDataOverseasMappingListByFidsAndNotInWork(fids, notInWork);
    }

/*    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByFileBaseIds(List<Long> fids, Long startId) {
        if (fids == null || fids.isEmpty()) {
            return new ArrayList<>();
        }
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("fileBaseId", fids);
        criteria.andGreaterThan("id", startId);
        example.orderBy("id").asc();
        return listMatchDataOverseasMappingMapper.selectByExampleAndRowBounds(example,new PageRowBounds(0,25000));
    }*/

    @Override
    @Transactional
    public void addImport(Map<String, String> u, List<String> titleList, Long fid, List<ListMatchDataOverseas> overseaList, List<ListMatchDataOverseasMapping> mappingList) {
        String workIdStr =u.get(titleList.get(8)) ;
        String societyCodeStr = u.get(titleList.get(9)) ;

        if(StringUtils.isEmpty(workIdStr) || StringUtils.isEmpty(societyCodeStr)){

            return ;
        }

        Long workId = Long.valueOf(workIdStr);
        Integer societyCode = Integer.valueOf(societyCodeStr);

        WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkId(workId,societyCode);
        if(wrkWork == null){
            return;
        }

        List<WrkWorkTitle> wrkWorkTitleList = wrkWorkTitleService.getWrkWorkTitleByWrkId(workId,societyCode) ;
        WrkWorkTitle wrkWorkTitle = null;
        if(CollectionUtils.isNotEmpty(wrkWorkTitleList)){
            wrkWorkTitle = wrkWorkTitleList.get(0);
            for(WrkWorkTitle wt : wrkWorkTitleList){
                if(wt.getSubTitleId() == 0){
                    wrkWorkTitle = wt;
                    break;
                }
            }
        }


        overseaList.add(getListMatchDataOverseas(u,titleList,fid,wrkWork,wrkWorkTitle));
        mappingList.add(getListMatchDataOverseasMapping(u,titleList,fid,wrkWork,wrkWorkTitle));
    }

    @Override
    public List<ListMatchDataOverseasMappingExport> getListMatchDataOverseasMappingForExport(Long startId, Long batch, Long matchWorkId, Integer matchWorkSoc, String matchWorkTitle,
                                                                                      String distributedIp, String distributedIpName, Integer matchIpSoc, String distNo,
                                                                                      Long fId, Integer remitSoc, String remitIpName, String remitWorkTItle,
                                                                                      String dataUniqueKey, Integer rejectCode, Integer status){
        /*List<Long> fIds = new ArrayList<>();
        if (StringUtils.isNotBlank(distNo) || fId != null) {
            fIds = listOverseasReceiptMapper.getFid(distNo, fId);
            if (org.springframework.util.CollectionUtils.isEmpty(fIds)) {
                if (fId != null){
                    fIds.add(fId);
                }else {
                    return new ArrayList<>();
                }
            }
        }*/
        return listMatchDataOverseasMappingMapper.getListMatchDataOverseasMappingForExport(matchWorkId,matchWorkSoc,matchWorkTitle,distributedIp,distributedIpName,matchIpSoc,remitSoc,
                remitIpName,startId,remitWorkTItle,dataUniqueKey,batch,fId,rejectCode,status,distNo);
    }

    @Override
    public List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingByIdAndDistNo(Long startId, String distNo) {

        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();

        criteria.andGreaterThan("id", startId);
        criteria.andEqualTo("distNo", distNo);
        example.orderBy("id").asc();
        return listMatchDataOverseasMappingMapper.selectByExampleAndRowBounds(example,new RowBounds(0,BATCH_SIZE_10000));
    }

    @Override
    public PageInfo<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingManual(Page page, ListMatchDataOverseas listMatchDataOverseas) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());

        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();

        Long fileBaseId = listMatchDataOverseas.getFileBaseId();
        criteria.andEqualTo("fileBaseId", fileBaseId);

        String sourceWorkCode = listMatchDataOverseas.getSourceWorkCode();
        if (sourceWorkCode != null && !"".equals(sourceWorkCode)) {
            criteria.andLike("workCode", ExampleUtil.exampleLikeAll(sourceWorkCode));
        }

        Integer status = listMatchDataOverseas.getStatus();
        if (status != null) {
            criteria.andEqualTo("status", status);
        }

        String matchWorkTitle = listMatchDataOverseas.getMatchWorkTitle();
        if (matchWorkTitle != null && !"".equals(matchWorkTitle)) {
            criteria.andLike("matchWorkTitle", ExampleUtil.exampleLikeAll(matchWorkTitle));
        }
        String originalTitle = listMatchDataOverseas.getOriginalTitle();
        if (StringUtils.isNotBlank(originalTitle)) {
            criteria.andLike("originalTitle", ExampleUtil.exampleLikeAll(originalTitle));
        }
        Long matchWorkId = listMatchDataOverseas.getMatchWorkId();
        if (Objects.nonNull(matchWorkId)) {
            criteria.andEqualTo("matchWorkId", matchWorkId);
        }
        Integer matchWorkSocietyCode = listMatchDataOverseas.getMatchWorkSocietyCode();
        if (Objects.nonNull(matchWorkSocietyCode)) {
            criteria.andEqualTo("matchWorkSocietyCode", matchWorkSocietyCode);
        }
        /*Long fileMappingId = listMatchDataOverseas.getFileMappingId();
        if (Objects.nonNull(fileMappingId)) {
            if (fileMappingId == -1L) {
                criteria.andNotEqualTo("fileMappingId", 0L);
            } else {
                criteria.andEqualTo("fileMappingId", fileMappingId);
            }
        }*/

//        example.orderBy("id").desc();
        List<ListMatchDataOverseasMapping> list = listMatchDataOverseasMappingMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> ids = list.stream().map(m -> m.getOverseasId()).collect(Collectors.toList());
            Map<Long,ListMatchDataOverseas> overseasMap = getListMatchDataOverseasByOverseasIds(ids);
            if(overseasMap != null){
                list.forEach(l -> {
                    if(overseasMap.containsKey(l.getOverseasId())){
                        l.setAuthorComposer(overseasMap.get(l.getOverseasId()).getAuthorComposer());
                    }
                });
            }
        }
        PageInfo<ListMatchDataOverseasMapping> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    @Override
    public void initOrDestroyWrkWorkIpshareMap(int action) {
        if(action == 1) wrkWorkIpshareMap = new HashMap<>();
        else {
            wrkWorkIpshareMap = null;
            if (paNameNoMap.size() > 1 << 20) {
                paNameNoMap = new HashMap<>();
            }
        }
    }

    @Override
    public void deleteByFileMappingId(Long fileMappingId) {
        ListMatchDataOverseasMapping delete = new ListMatchDataOverseasMapping();
        delete.setFileMappingId(fileMappingId);
        listMatchDataOverseasMappingMapper.delete(delete);
    }

    @Override
    public void deleteByFileMappingIds(List<Long> fileMappingIds) {
        Example example = new Example(ListMatchDataOverseasMapping.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("fileMappingId",fileMappingIds);
        listMatchDataOverseasMappingMapper.deleteByExample(example);
    }


    public ListMatchDataOverseas getListMatchDataOverseas( Map<String, String> u, List<String> titleList, Long fid,WrkWork wrkWork, WrkWorkTitle wrkWorkTitle){
        ListMatchDataOverseas lo = new ListMatchDataOverseas() ;
//        lo.setRemitDistNo(u.get(titleList.get(0)));
        lo.setRemitSociety(StringUtils.isEmpty(u.get(titleList.get(1))) ? 0 : Integer.valueOf(u.get(titleList.get(1))));
        lo.setFileBaseId(fid);
        lo.setStatus(1);
        lo.setFileMappingId(0L);
        lo.setReceiptSociety(161);
        lo.setMatchScore(new BigDecimal(30));
        lo.setOriginalTitle(u.get(titleList.get(3)));
        lo.setSourceWorkCode(u.get(titleList.get(2)));

        lo.setMatchWorkId(wrkWork.getWorkId());
        lo.setMatchWorkSocietyCode(wrkWork.getWorkSocietyCode());
        lo.setMatchWorkType(wrkWork.getWorkType());
        if(wrkWorkTitle != null){
            lo.setMatchWorkTitleId(wrkWorkTitle.getId());
            lo.setMatchSubTitleId(wrkWorkTitle.getSubTitleId());
            lo.setMatchWorkTitle(StringUtils.isEmpty(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitleEn() : wrkWorkTitle.getTitle());
        }

        String overseasMd5 = MatchMd5.getManullyOverseasMd5(lo);
        lo.setDataUniqueKey(overseasMd5);
        lo.init();

        return lo;
    }

    public Map<Long,ListMatchDataOverseas> getListMatchDataOverseasByOverseasIds(List<Long> ids){
        Example example = new Example(ListMatchDataOverseas.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        List<ListMatchDataOverseas> list = listMatchDataOverseasMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().collect(Collectors.toMap(ListMatchDataOverseas::getId, Function.identity()));
        }
        return null;
    }

    public ListMatchDataOverseasMapping getListMatchDataOverseasMapping( Map<String, String> u, List<String> titleList, Long fid,WrkWork wrkWork, WrkWorkTitle wrkWorkTitle){
        ListMatchDataOverseasMapping lm = new ListMatchDataOverseasMapping();
        lm.setIpSocietyCode(StringUtils.isEmpty(u.get(titleList.get(1))) ? 0 : Integer.valueOf(u.get(titleList.get(1))));
        lm.setIpName(u.get(titleList.get(4)));
        lm.setIpNameNo(u.get(titleList.get(5)));
        lm.setShareRatio(u.get(titleList.get(6)) == null ? BigDecimal.ZERO : new BigDecimal(u.get(titleList.get(6))));
        lm.setAmount(u.get(titleList.get(7)) == null ? BigDecimal.ZERO : new BigDecimal(u.get(titleList.get(7))));
        lm.setMatchIpNameNo(u.get(titleList.get(10)));
        lm.setFileBaseId(fid);
        lm.setFileMappingId(0L);
        lm.setOriginalTitle(u.get(titleList.get(3)));
        lm.setWorkCode(u.get(titleList.get(2)));

        lm.setMatchWorkId(wrkWork.getWorkId());
        lm.setMatchWorkSocietyCode(wrkWork.getWorkSocietyCode());
        if(wrkWorkTitle != null){
            lm.setMatchWorkTitleId(wrkWorkTitle.getId());
        }

        String ipNameNo = u.get(titleList.get(10));
        if (StringUtils.isNotBlank(ipNameNo)) {
            lm.setMatchIpNameNo(ipNameNo);
            MbrIpName mbrIpName = mbrIpNameService.getIpNameByIpNameNo(ipNameNo);
            if (Objects.nonNull(mbrIpName)) {
                String nameType = mbrIpName.getNameType();
                lm.setMatchIpName(StringUtils.isBlank(mbrIpName.getChineseName()) ? mbrIpName.getName() : mbrIpName.getChineseName());
                lm.setMatchNameType(nameType);
                if (StringUtils.equals("PA", nameType)) {
                    lm.setMatchPaNameNo(ipNameNo);
                }
            }
        }

        lm.init();
        return lm;
    }

    public List<ListMatchDataOverseas> getListMatchDataOverseasByMd5(String dataUniqueKey) {
        Example example = new Example(ListMatchDataOverseas.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataUniqueKey", dataUniqueKey);
        return listMatchDataOverseasMapper.selectByExample(example);
    }

    private List<ListMatchDataOverseasUniq> getListMatchDataOverseasUniqByMd5(String dataUniqueKey) {
        Example example = new Example(ListMatchDataOverseasUniq.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("dataUniqueKey", dataUniqueKey);
        return listMatchDataOverseasUniqMapper.selectByExample(example);
    }

    public void setMatchAndUnmatch(Set<Long> fileBaseIds){
        for(Long fileBaseId : fileBaseIds){

            ListOverseasFileBase listOverseasFileBase = listOverseasFileBaseMapper.selectByPrimaryKey(fileBaseId);
            ListOverseasFileBase lofb  = listOverseasFileBaseMapper.getCountUnmatchedByFid(fileBaseId);
            Integer matchedIpCount = lofb.getMatched();
            Integer unmatchedIpCount = lofb.getNormalAdjIpNumber() - matchedIpCount;
            BigDecimal ipMatchRate = BigDecimal.ZERO;
            if(matchedIpCount != 0 &&  listOverseasFileBase.getNormalAdjIpNumber() != 0){
                ipMatchRate = new BigDecimal(matchedIpCount * 100).divide(new BigDecimal(listOverseasFileBase.getNormalAdjIpNumber()),6, RoundingMode.HALF_UP);
            }
            listOverseasFileBase.setMatched(matchedIpCount);
            listOverseasFileBase.setUnmatched(unmatchedIpCount);
            listOverseasFileBase.setIpMatchRate(ipMatchRate);

            listOverseasFileBase.setAmendTime(new Date());

            listOverseasFileBaseMapper.updateByPrimaryKey(listOverseasFileBase);
        }
    }

    public String resortedComposer(String mappingComposer){

        if(StringUtils.isBlank(mappingComposer)){
            return mappingComposer;
        }

        if(mappingComposer.indexOf(" ") == -1){
            return mappingComposer;
        } else {
            String[] caArr = mappingComposer.split(" ");
            Arrays.sort(caArr);  // 对数组进行排序
            return String.join("", caArr);
        }
    }

    public boolean ipNameMatch(String remitName, WrkWorkIpShare wrkWorkIpShare){

        String workName = StringUtils.isNotBlank(wrkWorkIpShare.getName()) ? wrkWorkIpShare.getName() : wrkWorkIpShare.getDummyName();

        if(StringUtils.isNotBlank(workName)){
            workName  = resortedComposer(workName);

            if(StringUtils.equals(remitName, workName)){
                return true;
            }
        }
        return false;
    }



}