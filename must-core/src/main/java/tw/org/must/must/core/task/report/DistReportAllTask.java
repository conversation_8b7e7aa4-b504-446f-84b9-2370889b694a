package tw.org.must.must.core.task.report;

import cn.miludeer.jsoncode.JsonCode;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.jasperreports.engine.JRException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.CommonUtils;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.LocalCommonMethodUtils;
import tw.org.must.must.common.util.excel.GenerateExcelUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.core.csv.GenerateCsvWriter;
import tw.org.must.must.core.parse.ParseCRDService;
import tw.org.must.must.core.service.dist.*;
import tw.org.must.must.core.service.dist.impl.DistAutopayNetPayMemberServiceImpl;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkIpRoyService;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkPointService;
import tw.org.must.must.core.service.distdata.DistListCopyFilterService;
import tw.org.must.must.core.service.export.DistAutoNetPayExportService;
import tw.org.must.must.core.service.list.ListBasicFileBaseService;
import tw.org.must.must.core.service.list.ListFilePathChangeService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasMappingService;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileBaseService;
import tw.org.must.must.core.service.listoverseas.ListOverseasReceiptDetailsService;
import tw.org.must.must.core.service.listoverseas.ListOverseasReceiptService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.report.DistReportCategoryRoyalitesService;
import tw.org.must.must.core.service.report.DistReportDistAccountSdSummaryListService;
import tw.org.must.must.core.service.report.DistReportService;
import tw.org.must.must.core.service.wrk.WrkIswcService;
import tw.org.must.must.core.service.wrk.WrkWorkRightService;
import tw.org.must.must.core.task.dist.DistGenerateTypePTODO;
import tw.org.must.must.model.dist.*;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.distdata.DistListCopyFilter;
import tw.org.must.must.model.list.ListBasicFileBase;
import tw.org.must.must.model.list.ListFilePathChange;
import tw.org.must.must.model.listoverseas.*;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.report.*;
import tw.org.must.must.model.report.distAutoPay.report730VO.Report730MainVO;
import tw.org.must.must.model.wrk.WrkIswc;
import tw.org.must.must.model.wrk.WrkWorkRight;
import tw.org.must.report.export.PdfReport;
import tw.org.must.report.export.model.dist.*;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class DistReportAllTask {

    private static final Logger log = LoggerFactory.getLogger(DistReportAllTask.class);
    @Autowired
    private DistReportDistAccountSdSummaryListService distReportDistAccountSdSummaryListService;
    @Autowired
    private DistDataCalcWorkPointService distDataCalcWorkPointService;
    @Autowired
    private ListFilePathChangeService listFilePathChangeService;
    @Autowired
    private ListBasicFileBaseService listBasicFileBaseService;
    @Autowired
    private DistDataCalcWorkIpRoyService distDataCalcWorkIpRoyService;
    @Autowired
    private DistParamNumberService distParamNumberService;
    @Autowired
    private DistParamCategoryService distParamCategoryService;
    @Autowired
    private DistReportCategoryRoyalitesService distReportCategoryRoyalitesService;

    @Autowired
    private WrkWorkRightService wrkWorkRightService;

    @Autowired
    private DistParamOverseasService distParamOverseasService;

    @Autowired
    private ListOverseasReceiptDetailsService listOverseasReceiptDetailsService;

    @Autowired
    private ListOverseasFileBaseService listOverseasFileBaseService;

    @Autowired
    private ListMatchDataOverseasMappingService listMatchDataOverseasMappingService;

    @Autowired
    private DistReportService distReportService ;

    @Autowired
    private DistGenerateTypePTODO distGenerateTypePTODO ;

    @Autowired
    private ParseCRDService parseCRDService ;

    @Autowired
    private ListOverseasReceiptService listOverseasReceiptService;

    @Autowired
    private MbrIpNameService mbrIpNameService;

    @Autowired
    private WrkIswcService wrkIswcService;

    @Autowired
    private DistListCopyFilterService distListCopyFilterService;

    @Autowired
    private DistCalcRetainService distCalcRetainService;

    @Autowired
    private DistParamInfoService distParamInfoService;

    @Autowired
    private DistOverseasIpShareService distOverseasIpShareService;

/*    @Autowired
    private DistOverseasCheckingIpNotInWorkService distOverseasCheckingIpNotInWorkService;*/

    public void init(){
        distDataCalcWorkPointService.init();
    }

    public void clear(){
        distDataCalcWorkPointService.clear();
    }


    public void generateReport(String distNo) {
        XxlJobLogger.log("distNo:【{}】，相关报表数据生成异步任务触发！", distNo);
        CompletableFuture.runAsync(() -> {
            distReportDistAccountSdSummaryListService.generateReport(distNo);
        });
    }


    public String generatePDF(String distNo, List<String> sourceTypes) {
        XxlJobLogger.log("distNo:【{}】，相关PDF报表生成异步任务触发！", distNo);
        List<ListFilePathChange> pathByStatus = listFilePathChangeService.getPathByStatus();
        if (CollectionUtils.isEmpty(pathByStatus)) {
            throw new MustException("导出PDF失败！无有效共享路径！");
        }
        String format = DateParse.format(new Date(), DateParse.patternTime);
        String  outParentFile = String.format("%s/DIST_PDF/%s%s%s", pathByStatus.get(0).getServerFilePath(), distNo, File.separator,format);
        File file = new File(outParentFile);
        if (!file.exists()) {
            file.mkdirs();
        }
        for (String sourceType : sourceTypes) {
            switch (sourceType) {
                case "P":
                case "I":
                    CompletableFuture.runAsync(() -> {
                        this.init();
                        this.generatePMemberPDF(outParentFile, distNo);
                        this.generatePSocietyPDF(outParentFile, distNo);
                        this.clear();
                        this.generateDistSummary(outParentFile, distNo);
//                        this.generateSingle(outParentFile, distNo);
                        this.generateCrd(outParentFile,distNo);
                        distGenerateTypePTODO.genrateTypeP(outParentFile,distNo);
                        distGenerateTypePTODO.genrateTypePSoc(outParentFile,distNo);
                    });
                    break;
                // TODO 未完待续
                case "O":
//                    CompletableFuture.runAsync(() -> {
                        this.generateWithoutDetailData(outParentFile,distNo);
                        this.generateAmountCheck(outParentFile,distNo);
                        this.generateIpNotInWork(outParentFile,distNo);
                        this.generateOverseasDistributionPaymentInformation(outParentFile,distNo);
                        this.generateOverseasWorkDetails(outParentFile,distNo);
                        this.generateCrossCheck(outParentFile,distNo);
//                        distGenerateTypePTODO.genrateTypeP(outParentFile,distNo);

                        this.init();
                        this.generatePMemberPDF(outParentFile, distNo);
                        this.generateOverSeasFiePDF(outParentFile, distNo);
                        this.clear();
                        this.generateDistSummary(outParentFile, distNo);
                        this.generateCrd(outParentFile,distNo);
                        distGenerateTypePTODO.genrateTypeP(outParentFile,distNo);
//                        distGenerateTypePTODO.genrateTypePSoc(outParentFile,distNo);
//                    });
                    break;
                case "SD":
                    break;
                case "SR":
                    break;
                case "M":
                    break;
                default:
                    break;
            }
        }
        return outParentFile;
    }


    public void generateCrd(String outParentFile, String distNo){
        log.info("生成CRD文档，distNO:{}",distNo);
        try{
            Map<String, List<String>> map = parseCRDService.generateForMember(distNo);
            if(distNo.startsWith("P") || distNo.startsWith("O")){
                Map<String,List<String>> mapM = parseCRDService.generate(distNo);
                map.putAll(mapM);
            }
            for(Map.Entry<String,List<String>> entry : map.entrySet()){
                String key = entry.getKey();
                List<String> value = entry.getValue();
                File crdFile = null;
                FileWriter fileWriter = null;

                String[] fileNames = key.split("/") ;
                String fileName = fileNames[0] ;
                String type = fileName.substring(0,4);
                fileName = fileName.substring(4);
                String soc = fileNames[1] ;
                String dateStr = DateParse.format(new Date(),DateParse.patternTime) ;
                String outFile = String.format("%s/%s/CRD_02.00_%s_%s_LOG_SOURCE_ALL_SHARE/%s/CRD%s%s161.020", outParentFile, distNo,distNo,type,fileName,dateStr,soc);
//                String outFile = String.format("%s\\%s\\CRD_02.00_%s_%s_LOG_SOURCE_ALL_SHARE\\%s\\CRD%s%s161.020", outParentFile, distNo,distNo,type,fileName,dateStr,soc);
                File file = new File(outFile);
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }

                try {
                    if (null == crdFile) {
                        crdFile = new File(outFile);
                    }

                    fileWriter = new FileWriter(crdFile, true);
                    for(String string : value){
                        fileWriter.write(string);
                        fileWriter.write(CommonUtils.LINE_BREAK);
                    }
                }catch (Exception e){
                    log.error("生成CRD文件出错：",e);
                }finally {
                    try {
                        fileWriter.flush();
                        fileWriter.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

            }

        } catch (Exception e){
            log.error("生成CRD文件出错：",e);
        }

        log.info("生成CRD文档完成，distNO:{}",distNo);
    }

    // MUST-4103
    // 数据来源表：dist_data_calc_work_ip_roy
    //
    //表内容数据来源说明：
    //
    //各POOL的實際分配比例:
    //
    //数据根据 分配参数配置中选定的source进行group by计算得到，使用Net dist Roy值作为统计
    //
    //國內, 海外與爭議之作者與出版公司的比例：
    //
    //根据海内外收入，ip role （分组项C和E。ip role 与分组项对应关系：A\CA\C\SA\AR ->C   ,   E\SE\PA\ ->E）分组统计
    public void generateDistSummary(String outParentFile, String distNo) {

        log.info("开始生成DIST_SUMMARY");
        try {
            SummaryPdf summaryPdf = generateSummaryPdf(distNo);

            List<JasperParam> jaspers = new ArrayList<>();
            jaspers.add(new JasperParam(JSON.parseObject(JSON.toJSONString(summaryPdf.getParam()), Map.class), JSON.toJSONString(summaryPdf.getSub()).getBytes()));
            String outFile = String.format("%s/%s/Others/DIST_SUMMARY.PDF", outParentFile, distNo);

            PdfReport.exprtPdf(outFile, jaspers, "/ireport/jrxml/dist/summary_main.jrxml");
        } catch (Exception e) {
            log.error("distNo:【{}】，相关PDF报表生成失敗！", distNo,e);
        }

    }

    public void generateReportCategoryRoyalites(DistParamInfo distParamInfo){

        String distNo = distParamInfo.getDistNo();

        if(StringUtils.isNotBlank(distNo)) {
            distReportCategoryRoyalitesService.clearByDistNo(distNo);
//            List<DistDataCalcWorkIpRoy> list = distDataCalcWorkIpRoyService.getByDistNo(distNo);
//            List<DistReportCategoryRoyalites> retRoyAmtSumList = distReportCategoryRoyalitesService.getRetRoyAmtSum(distNo) ;

            int sacle = distParamInfo.getScale();

            List<DistParamCategory> distParamCategoryList = distParamCategoryService.selectDistParamCategoryByDistNo(distNo) ;
            Map<String,BigDecimal> distRoySumMap = distParamCategoryList.stream().collect(Collectors.toMap(DistParamCategory:: getListCategoryCode,DistParamCategory::getNetDistAmount));
            Map<String,String> rightMap = new HashMap<>() ;
            Long startId = 0L;
            Map<String,DistReportCategoryRoyalites> royalitesMap = new HashMap<>() ;
            Map<String,Set<String>> distWorkMap = new HashMap<>();
            Map<String,Set<String>> noDistWorkMap = new HashMap<>();
            while(true){
                List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getDistDataCalcWorkIpRoyListByCategoryCodeListAndId(null,distParamInfo.getDistNo(),startId);
                if (distDataCalcWorkIpRoyList == null || distDataCalcWorkIpRoyList.isEmpty()) {
                    break;
                }

                startId = distDataCalcWorkIpRoyList.get(distDataCalcWorkIpRoyList.size() -1).getId();
                for(DistDataCalcWorkIpRoy roy: distDataCalcWorkIpRoyList){

                    String categoryCode = roy.getCategoryCode();
                    DistReportCategoryRoyalites royalites = royalitesMap.get(categoryCode) ;

                    if(roy.getDistRoy() == null) roy.setDistRoy(BigDecimal.ZERO);
                    if(roy.getNetPoint() == null) roy.setNetPoint(BigDecimal.ZERO);
                    if(roy.getUpaAmt() == null) roy.setUpaAmt(BigDecimal.ZERO);

                    String distributable = rightMap.get(roy.getWorkUniqueKey() + roy.getRightType());

                    if(royalites == null){
                        royalites = new DistReportCategoryRoyalites();
                        royalites.setTotalPointSum(roy.getNetPoint());
                        royalites.setUpaAmtSum(roy.getUpaAmt());
                        royalites.setPool(roy.getPoolCode());
                        royalites.setDistRoySum(BigDecimal.ZERO);
                        royalites.setSdDistRoySum(BigDecimal.ZERO);
                        royalites.setNormalDistRoySum(BigDecimal.ZERO);
                        if(roy.getSd().equals("Y")){
                            royalites.setSdDistRoySum(roy.getDistRoy());
                        }else {
                            royalites.setNormalDistRoySum(roy.getDistRoy());
                        }

                        royalites.setCategoryCode(categoryCode);
                        royalitesMap.put(categoryCode,royalites) ;
                    } else {
                        royalites.setTotalPointSum(roy.getNetPoint().add(royalites.getTotalPointSum()));
                        royalites.setUpaAmtSum(roy.getUpaAmt().add(royalites.getUpaAmtSum()));
                        if(roy.getSd().equals("Y")){
                            royalites.setSdDistRoySum(roy.getDistRoy().add(royalites.getSdDistRoySum()));
                        }else {
                            royalites.setNormalDistRoySum(roy.getDistRoy().add(royalites.getNormalDistRoySum()));
                        }
                    }

                    if(distributable == null){
                        WrkWorkRight wrkWorkRight = wrkWorkRightService.getWrkWorkRightByWorkUniqueAndRightType( roy.getWorkUniqueKey(),roy.getRightType()) ;
                        distributable = wrkWorkRight == null ? "N" : wrkWorkRight.getDistributable() == 0 ? "N" : "Y" ;
                        rightMap.put(roy.getWorkUniqueKey() + roy.getRightType(), distributable) ;
                    }

                    if(distributable.equals("N")){
                        Set<String> distWorkSet = distWorkMap.get(categoryCode) ;
                        if(distWorkSet == null){
                            distWorkSet = new HashSet<>();
                            royalites.setNoOfActPointsSum(roy.getNetPoint());
                            distWorkMap.put(categoryCode,distWorkSet) ;
                        } else {
                            royalites.setNoOfActPointsSum(roy.getNetPoint().add(royalites.getNoOfActPointsSum()));
                        }
                        distWorkSet.add(roy.getWorkUniqueKey());
                    }else{
                        Set<String> noDistWorkSet = noDistWorkMap.get(categoryCode) ;
                        if(noDistWorkSet == null){
                            noDistWorkSet = new HashSet<>();
                            royalites.setNoOfActIpointsSum(roy.getNetPoint());
                            noDistWorkMap.put(categoryCode,noDistWorkSet) ;
                        } else {
                            royalites.setNoOfActIpointsSum(roy.getNetPoint().add(royalites.getNoOfActIpointsSum()));
                        }
                        noDistWorkSet.add(roy.getWorkUniqueKey());
                    }
                }
            }

            List<DistParamCategory> distParamCategorys = distParamCategoryService.selectDistParamCategoryByDistNo(distNo);
            for(Map.Entry<String,DistReportCategoryRoyalites> entry : royalitesMap.entrySet()){
                DistReportCategoryRoyalites royalites = entry.getValue();
                royalites.setDistNo(distNo);
                royalites.setNoOfActWork(distWorkMap.getOrDefault(entry.getKey(),new HashSet<>()).size());
                royalites.setNoOfActIwork(noDistWorkMap.getOrDefault(entry.getKey(),new HashSet<>()).size());
                royalites.setDistRoySum(distRoySumMap.get(entry.getKey()));

                if(royalites.getTotalPointSum().compareTo(BigDecimal.ZERO) == 1){
                    royalites.setPtSumValue(royalites.getDistRoySum().divide(royalites.getTotalPointSum(),6,RoundingMode.HALF_UP));
//                        sdDistRoySum.set(sdDistRoySum.get().multiply(ptValue.get()));
//                        normalDistRoySum.set(normalDistRoySum.get().multiply(ptValue.get()));
                }

                if(!CollectionUtils.isEmpty(distParamCategorys)){
                    DistParamCategory distParamCategory = distParamCategorys.get(0);
                    if(distParamCategory.getListStartTime() != null){
                        royalites.setFromPerformDate(distParamCategory.getListStartTime());
                    }

                    if(distParamCategory.getListEndTime() != null){
                        royalites.setToPerformDate(distParamCategory.getListEndTime());
                    }

                    if(royalites.getFromPerformDate() == null){
                        royalites.setFromPerformDate(distParamInfo.getDistListStartTime());
                    }

                    if(royalites.getToPerformDate() == null){
                        royalites.setToPerformDate(distParamInfo.getDistListEndTime());
                    }

                    distReportCategoryRoyalitesService.add(royalites);
                }
            }
        }

    }

    private SummaryPdf generateSummaryPdf(String distNo) {
        SummaryPdf summaryPdf = new SummaryPdf();
        if(StringUtils.isNotBlank(distNo)) {
            //  && StringUtils.equals(x.getIsDist(), "Y") 放开，测试
            List<DistDataCalcWorkIpRoy> list = distDataCalcWorkIpRoyService.getByDistNo(distNo).stream().filter(x -> null != x.getDistRoy()).collect(Collectors.toList());
            if(!list.isEmpty()) {
                SummaryParam param = new SummaryParam();
                param.setDistNo(distNo);
                param.setDate(DateParse.format(new Date(), "yyyyMMdd HH:mm"));
                DistParamNumber distParamNumber = distParamNumberService.getDistParamNumber(distNo);
                param.setYear(distParamNumber.getDistYear());
                BigDecimal sd = list.stream().filter(x -> StringUtils.equals(x.getSd(), "Y")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal must = list.stream().filter(x -> !StringUtils.equals(x.getSd(), "Y") && StringUtils.equals(x.getIpSocietyCode(), "161")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal overseas = list.stream().filter(x -> !StringUtils.equals(x.getSd(), "Y") && !StringUtils.equals(x.getIpSocietyCode(), "161")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal all = sd.add(must).add(overseas);
                param.setSd(bigDecimaltoString(sd));
                param.setMust(bigDecimaltoString(must));
                param.setOverseas(bigDecimaltoString(overseas));
                param.setSummary(bigDecimaltoString(all));
                param.setSummaryShare("100%");
                param.setTotal(bigDecimaltoString(all));
                param.setTotalShare("100%");
                if(all.compareTo(BigDecimal.ZERO) == 1) {
                    param.setSdShare(String.format("%s%%", sd.divide(all, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toPlainString()));
                    param.setMustShare(String.format("%s%%", must.divide(all, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toPlainString()));
                    param.setOverseasShare(String.format("%s%%", overseas.divide(all, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toPlainString()));
                }
                List<DistParamCategory> paramCategoryList = distParamCategoryService.getDistParamCategoryList(distNo, null,null);
                Map<String,String> sourceCategoryMap = paramCategoryList.stream().collect(Collectors.toMap(DistParamCategory::getListCategoryCode,DistParamCategory::getListSourceName,(a,b)-> a));
                if(!CollectionUtils.isEmpty(paramCategoryList)) {
                    BigDecimal allocateSummary = paramCategoryList.stream().map(DistParamCategory::getNetDistAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    param.setAllocatedSummary(bigDecimaltoString(allocateSummary));
                    param.setDifference(bigDecimaltoString(all.subtract(allocateSummary)));
                } else if(distNo.startsWith("O")){
                    sourceCategoryMap.put("OSD","OVERSEAS DISTRIBUTION");

                    List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distNo);
                    if(CollectionUtils.isEmpty(distParamOverseasList)){
                        BigDecimal allocateSummary = BigDecimal.ZERO;
                        param.setAllocatedSummary(bigDecimaltoString(allocateSummary));
                        param.setDifference(bigDecimaltoString(all));
                    } else {
                        BigDecimal allocateSummary = distParamOverseasList.stream().map(DistParamOverseas::getNetRoy).reduce(BigDecimal.ZERO, BigDecimal::add);
                        param.setAllocatedSummary(bigDecimaltoString(allocateSummary));
                        param.setDifference(bigDecimaltoString(all));
                    }

                    List<DistListCopyFilter> distListCopyFilters = distListCopyFilterService.selectDistListCopyFilterListByDistNo(distNo);
                    Map<String,String> sourceCategoryMap2 = distListCopyFilters.stream().collect(Collectors.toMap(DistListCopyFilter::getToListCategoryCode,DistListCopyFilter::getToListSourceCode,(a,b)-> a));
                    if(!sourceCategoryMap2.isEmpty()){
                        sourceCategoryMap.putAll(sourceCategoryMap2);
                    }
                }
                summaryPdf.setParam(param);

                SummarySub summarySub = new SummarySub();

                Map<String, List<DistDataCalcWorkIpRoy>> map = list.stream().filter(x -> null != x.getDistRoy()).collect(Collectors.groupingBy(y -> sourceCategoryMap.get(y.getCategoryCode())));
                if(!map.isEmpty()) {
                    List<PoolPdf> pools = new ArrayList<>(map.size());
                    for (Map.Entry<String, List<DistDataCalcWorkIpRoy>> entry : map.entrySet()) {
                        PoolPdf poolPdf = new PoolPdf();
//                        ListCategory categoryByCode = listCategoryService.getCategoryByCode(entry.getKey());
//                        poolPdf.setPool(categoryByCode.getSourceName());
                        poolPdf.setPool(entry.getKey());
                        List<DistDataCalcWorkIpRoy> workIpRoys = entry.getValue();
                        BigDecimal sd1 = workIpRoys.stream().filter(x -> StringUtils.equals(x.getSd(), "Y")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        BigDecimal must1 = workIpRoys.stream().filter(x -> !StringUtils.equals(x.getSd(), "Y") && StringUtils.equals(x.getIpSocietyCode(), "161")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        BigDecimal overseas1 = workIpRoys.stream().filter(x -> !StringUtils.equals(x.getSd(), "Y") && !StringUtils.equals(x.getIpSocietyCode(), "161")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        BigDecimal all1 = sd1.add(must1).add(overseas1);
                        poolPdf.setSd(bigDecimaltoString(sd1));
                        poolPdf.setMust(bigDecimaltoString(must1));
                        poolPdf.setOverseas(bigDecimaltoString(overseas1));
                        poolPdf.setSummary(bigDecimaltoString(all1));
                        poolPdf.setSummaryShare("100%");
                        if(all1.compareTo(BigDecimal.ZERO) == 1) {
                            poolPdf.setSdShare(String.format("%s%%", sd1.divide(all1, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toPlainString()));
                            poolPdf.setMustShare(String.format("%s%%", must1.divide(all1, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toPlainString()));
                            poolPdf.setOverseasShare(String.format("%s%%", overseas1.divide(all1, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toPlainString()));
                        }
                        pools.add(poolPdf);
                    }
                    summarySub.setPools(pools);
                }
                List<IpsPdf> ips = new ArrayList<>();
                // A\CA\C\SA\AR ->C   ,   E\SE\PA\ ->E
                BigDecimal mustC = list.stream().filter(x -> StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "N")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "A", "CA", "C", "SA", "AR")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal mustE = list.stream().filter(x -> StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "N")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "E", "SE", "PA")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal mustSdC = list.stream().filter(x -> StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "Y")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "A", "CA", "C", "SA", "AR")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal mustSdE = list.stream().filter(x -> StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "Y")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "E", "SE", "PA")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                BigDecimal overseasC = list.stream().filter(x -> !StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "N")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "A", "CA", "C", "SA", "AR")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal overseasE = list.stream().filter(x -> !StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "N")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "E", "SE", "PA")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal overseasSdC = list.stream().filter(x -> !StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "Y")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "A", "CA", "C", "SA", "AR")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal overseasSdE = list.stream().filter(x -> !StringUtils.equals(x.getIpSocietyCode(), "161") && StringUtils.equals(x.getSd(), "Y")
                        && StringUtils.equalsAnyIgnoreCase(x.getWorkIpRole(), "E", "SE", "PA")).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal allIpsPdf = mustC.add(mustE).add(mustSdC).add(mustSdE).add(overseasC).add(overseasE).add(overseasSdC).add(overseasSdE);
                if(mustC.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(mustC, allIpsPdf, "MUST", "C"));
                }
                if(mustE.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(mustE, allIpsPdf, "MUST", "E"));
                }
                if(mustSdC.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(mustSdC, allIpsPdf, "MUST-SD", "C"));
                }
                if(mustSdE.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(mustSdE, allIpsPdf, "MUST-SD", "E"));
                }
                if(overseasC.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(overseasC, allIpsPdf, "OVERSEAS", "C"));
                }
                if(overseasE.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(overseasE, allIpsPdf, "OVERSEAS", "E"));
                }
                if(overseasSdC.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(overseasSdC, allIpsPdf, "OVERSEAS-SD", "C"));
                }if(overseasSdE.compareTo(BigDecimal.ZERO) > 0) {
                    ips.add(generateIpsPdf(overseasSdE, allIpsPdf, "OVERSEAS-SD", "E"));
                }
                summarySub.setIps(ips);
                summaryPdf.setSub(summarySub);
            }

        }
        return summaryPdf;
    }

    private IpsPdf generateIpsPdf(BigDecimal mustC, BigDecimal allIpsPdf, String name, String role) {
        IpsPdf ipsPdf = new IpsPdf();
        ipsPdf.setName(name);
        ipsPdf.setRole(role);
        ipsPdf.setAmount(bigDecimaltoString(mustC));
        if(allIpsPdf.compareTo(BigDecimal.ZERO) > 0) {
            ipsPdf.setShare(String.format("%s%%", bigDecimaltoString(mustC.divide(allIpsPdf, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)))));
        }
        return ipsPdf;
    }

    /**
     * 导出单场次 csv 文件， MUST-4045
     * @param outParentFile
     * @param distNo
     */
    public void generateSingle(String outParentFile, String distNo) {
        List<String> fileBaseIds = distDataCalcWorkPointService.getFileBaseIdList(distNo);
        if (CollectionUtils.isEmpty(fileBaseIds)) {
            return;
        }
        List<ListBasicFileBase> bases = listBasicFileBaseService.getSingleListByIds(fileBaseIds);
        if (CollectionUtils.isEmpty(bases)) {
            return;
        }
        // 再次防止导出group为空的情况，加默认值
        Set<String> groupSet = bases.stream().filter(x -> StringUtils.isNotBlank(x.getExtJson())).map(y -> JsonCode.getValue(y.getExtJson(), "$.group")).filter(z -> StringUtils.isNotBlank(z)).collect(Collectors.toSet());
        int groupStart = 1;
        for (ListBasicFileBase fileBase : bases) {
            fileBase.initConcertTitle();
            String group = JsonCode.getValue(fileBase.getExtJson(), "$.group");
            if (StringUtils.isBlank(group)) {
                for (int i = groupStart; i < Integer.MAX_VALUE; i++) {
                    String groupI = String.valueOf(i);
                    if (groupSet.add(groupI)) {
                        fileBase.setExtJson(groupI); // 暂时导出放一下
                        groupStart = i + 1;
                        break;
                    }
                }
            }
        }
        String[] title = new String[]{"PERFDATE", "TITLE", "GROUP", "REF_NO", "LOGNUM_HDR"};
        String[] titleKey = new String[]{"listFileStartTime", "concertTitle", "extJson", "categoryCode", "id"};
        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s/%s/DIVA_Temp/single_perf_560.CSV", outParentFile, distNo);
        File file = new File(filePath);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        GenerateCsvWriter<ListBasicFileBase> generateCsvWriter = null;
        try {
            generateCsvWriter = new GenerateCsvWriter<>(title, titleKey, filePath);
            generateCsvWriter.initTitle();
            JSON.DEFFAULT_DATE_FORMAT = "yyyyMMdd";
            generateCsvWriter.writeList(bases);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            generateCsvWriter.close();
        }

    }


    /**
     * 导出PDF，P协会
     *
     * @param outParentFile
     * @param distNo
     */
    public void generatePSocietyPDF(String outParentFile, String distNo) {
        log.info("开始生成协会PDF");
        try{
            List<DistReportPAndSocietyData> distReportPAndSocietyData = distDataCalcWorkPointService.exportDistReportPAndSocietyData(distNo);
            if(CollectionUtils.isEmpty(distReportPAndSocietyData)){
                log.info("没有协会数据");
            }
            String distType = distNo.substring(0,1);
            String filePath = "/ireport/jrxml/royaltyDetails/" + distType + "Society_main.jrxml";
            distReportPAndSocietyData.stream().forEach(data -> {
                if(data.getWorkData().size() > 0){
                    List<JasperParam> jaspers = new ArrayList<>();
                    List<WorkData> WorkData = data.getWorkData();
                    Map<String,Object> param = new HashMap<>();
                    DistReportPAndSocietyData.Header header = data.getHeader();
                    param.put("distributionNo", header.getDistributionNo());
                    param.put("date", DateParse.format(new Date()));
                    param.put("societyName", header.getSocietyName());
                    param.put("societyCode", header.getSocietyCode());
                    Summary summary = data.getSummary();
                    param.put("totalWorks", String.valueOf(summary.getTotalWorks()));
//                    param.put("totalRoyMap", String.valueOf(summary.getTotalRoyMap()));
                    param.put("total", summary.getTotalFormat());
                    param.put("tvTotal", summary.getTvTotal());
                    param.put("radioTotal", summary.getRadioTotal());
                    param.put("concertTotal", summary.getConcertTotal());
                    param.put("karaokeTotal", summary.getKaraokeTotal());
                    param.put("airlineTotal", summary.getAirlineTotal());
                    param.put("otherTotal", summary.getOthersTotal());
                    jaspers.add(new JasperParam(param, JSON.toJSONString(WorkData).getBytes()));
                    String outFile = String.format("%s/%s/Soc_Oth/%s_%s.pdf", outParentFile,distNo, header.getSocietyName(), distNo);
                    try {
                        PdfReport.exprtPdf(outFile, jaspers, filePath);
                    } catch (Exception e) {
                        log.error("distNo:【{}】，相关PDF报表生成失敗！", distNo,e);
                    }
                }
            });
        }catch (Exception e ){
            log.info("生成协会PDF失败：",e);
        }
    }


    /**
     * 导出PDF，P个人
     *
     * @param outParentFile
     * @param distNo
     */
    public void generatePMemberPDF(String outParentFile, String distNo) {
        log.info("开始生成个人PDF，distNo：{}", distNo);
        try{
            log.info("调用exportDistReportPAndIPData查询数据，distNo：{}", distNo);
            List<DistReportPAndIPData> distReportPAndSocietyData = distDataCalcWorkPointService.exportDistReportPAndIPData(distNo);
            log.info("exportDistReportPAndIPData查询结果，数据条数：{}", distReportPAndSocietyData != null ? distReportPAndSocietyData.size() : 0);

            String distType = distNo.substring(0,1);
            log.info("识别分配类型：{}", distType);
            String fileName = "";
            switch (distType){
                case "P":
                    fileName =  distType + "Personal_main.jrxml";
                    break;
                case "S":
                    fileName =  distType + "Society_main.jrxml";
                    break;
                case "O":
                    fileName =  "MOverSeas_main.jrxml";
                    log.info("识别为MOverSeas类型，调用generateMOverSeasPDF方法");
                    this.generateMOverSeasPDF(distReportPAndSocietyData,outParentFile, distNo,fileName);
                    return;
            }
            String filePath = "/ireport/jrxml/royaltyDetails/" + fileName;

            distReportPAndSocietyData.stream().forEach(data -> {
                if(data.getWorkData().size() > 0){
                    List<JasperParam> jaspers = new ArrayList<>();
                    List<WorkData> WorkData = data.getWorkData();
                    Map<String,Object> param = new HashMap<>();
                    DistReportPAndIPData.Header header = data.getHeader();
                    param.put("distributionNo", header.getDistributionNo());
                    param.put("date", DateParse.format(new Date()));
                    param.put("mustMemberName", header.getMustMemberName());
                    param.put("ipBaseNo", header.getIpBaseNo());
                    param.put("paNameNo", header.getPaNameNo());
                    Summary summary = data.getSummary();
                    param.put("totalWorks", String.valueOf(summary.getTotalWorks()));
//                    param.put("totalRoyMap", String.valueOf(summary.getTotalRoyMap()));
                    param.put("total", summary.getTotalFormat());
                    param.put("tvTotal", summary.getTvTotal());
                    param.put("radioTotal", summary.getRadioTotal());
                    param.put("concertTotal", summary.getConcertTotal());
                    param.put("karaokeTotal", summary.getKaraokeTotal());
                    param.put("airlineTotal", summary.getAirlineTotal());
                    param.put("otherTotal", summary.getOthersTotal());

                    jaspers.add(new JasperParam(param, JSON.toJSONString(WorkData).getBytes()));
                    String outFile = String.format("%s/%s/Roy_Dtl/%s_%s_%s.pdf", outParentFile, distNo,header.getMemberNo(),header.getIpBaseNo(), distNo);
                    try {
                        PdfReport.exprtPdf(outFile, jaspers, filePath);
                    } catch (Exception e) {
                        log.error("distNo:【{}】，相关PDF报表生成失敗！", distNo,e);
                    }
                }
            });
        }catch (Exception e){
            log.info("生成个人PDF失败:",e);
        }
    }

    public void generateOverSeasFiePDF(String outParentFile, String distNo) {
        log.info("开始生成MOverSeas的PDF");

        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);

        if(distParamInfo == null){
            log.info("generateOverSeasFiePDF，分配编号不存在");
            return;
        }

        List<DistCalcRetain> distCalcRetainList = distCalcRetainService.listWithDistAndType(distNo,"S");
        if (distCalcRetainList == null || distCalcRetainList.size() == 0){
            log.info("generateOverSeasFiePDF，没有要输出的数据");
            return;
        }

        List<DistReportFieData> reportFieDataList = new ArrayList<>();
        for(DistCalcRetain distCalcRetain : distCalcRetainList){
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getByRetanId(distCalcRetain.getId());

            distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyList.stream().
                    filter(d-> d.getDistRoy().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(distDataCalcWorkIpRoyList)){
                continue;
            }

            DistReportFieData distReportFieData = new DistReportFieData();
            DistReportFieData.Header header = new DistReportFieData.Header();
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            header.setIpBaseNo(distCalcRetain.getIpBaseNo());
            header.setMustMemberName(distCalcRetain.getIpChineseName());
            distReportFieData.setHeader( header);

            Map<Long,DistDataCalcWorkIpRoy> workUniqueKeyMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.toMap(DistDataCalcWorkIpRoy::getFileMappingId,d -> d));
            List<Long> fileMappingIds = distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getFileMappingId).distinct().collect(Collectors.toList());
            List<ListMatchDataOverseasMapping> listMatchDataOverseasMappings = listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByMappingIds(fileMappingIds);
            Map<String,List<ListMatchDataOverseasMapping>> listMatchDataOverseasMappingMap = listMatchDataOverseasMappings.stream().collect(Collectors.groupingBy(ListMatchDataOverseasMapping::getDataUniqueKey));

            List<FieWorkData> workDataList = new ArrayList<>();

            for(Map.Entry<String,List<ListMatchDataOverseasMapping>> entry : listMatchDataOverseasMappingMap.entrySet()){
                List<ListMatchDataOverseasMapping> mappingList = entry.getValue();
                ListMatchDataOverseasMapping mapping = mappingList.get(0);

                FieWorkData workData = new FieWorkData();
                workData.setRemitWorkNo(mapping.getWorkCode());
                workData.setRemitWorkTitle(mapping.getOriginalTitle());
                // 设置匹配作品信息（初始为空，后续会根据匹配情况设置）
                workData.setWorkTitle("");
                workData.setWorkNo("");

                List<WorkIpRoy> feeInErrorList = new ArrayList<>();
                workData.setFeeInErrorList(feeInErrorList);
                workDataList.add(workData);
                for(ListMatchDataOverseasMapping listMatchDataOverseasMapping : mappingList){
//                    ListMatchDataOverseasMapping mapping = listMatchDataOverseasMappingMap.get(distDataCalcWorkIpRoy.getFileMappingId());
                    WorkIpRoy rimitWorkIp = new WorkIpRoy();
                    rimitWorkIp.setIpNameNo(listMatchDataOverseasMapping.getIpNameNo());
                    rimitWorkIp.setIpName(listMatchDataOverseasMapping.getIpName());
                    rimitWorkIp.setShare(listMatchDataOverseasMapping.getShareRatio() + "%");
                    rimitWorkIp.setSociety(listMatchDataOverseasMapping.getIpSocietyCode() + "");
                    rimitWorkIp.setStatus(listMatchDataOverseasMapping.getWorkIpRole());
                    rimitWorkIp.setTv("$0.00");
                    rimitWorkIp.setRadio("$0.00");
                    rimitWorkIp.setConcert("$0.00");
                    rimitWorkIp.setKaraoke("$0.00");
                    rimitWorkIp.setAirline("$0.00");
                    rimitWorkIp.setOthers("$" + listMatchDataOverseasMapping.getAmount());
                    // 计算总计
                    rimitWorkIp.setTotal("$" + listMatchDataOverseasMapping.getAmount());
                    feeInErrorList.add(rimitWorkIp);

                    WorkIpRoy rejectWorkIp = new WorkIpRoy();
                    rejectWorkIp.setIpName(listMatchDataOverseasMapping.getRejectMessage());
                    rejectWorkIp.setShare(listMatchDataOverseasMapping.getShareRatio() + "%");
                    rejectWorkIp.setIpNameNo(listMatchDataOverseasMapping.getIpNameNo());
                    rejectWorkIp.setSociety(listMatchDataOverseasMapping.getIpSocietyCode() + "");
                    rejectWorkIp.setStatus(listMatchDataOverseasMapping.getWorkIpRole());
                    rejectWorkIp.setTv("$0.00");
                    rejectWorkIp.setRadio("$0.00");
                    rejectWorkIp.setConcert("$0.00");
                    rejectWorkIp.setKaraoke("$0.00");
                    rejectWorkIp.setAirline("$0.00");

                    DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = workUniqueKeyMap.get(listMatchDataOverseasMapping.getFileMappingId());
                    if(distDataCalcWorkIpRoy != null){
                        String distRoyAmount = "$" + distDataCalcWorkIpRoy.getDistRoy().setScale(2, RoundingMode.HALF_UP).toString();
                        rejectWorkIp.setOthers(distRoyAmount);
                        rejectWorkIp.setTotal(distRoyAmount);
                    } else {
                        rejectWorkIp.setOthers("$0.00");
                        rejectWorkIp.setTotal("$0.00");
                    }
                    feeInErrorList.add(rejectWorkIp);
                }

                String matchWorkUniqueKey = Constants.getWorkUniqueKey(mapping.getMatchWorkSocietyCode(),mapping.getMatchWorkId());
                if(StringUtils.isNotBlank(matchWorkUniqueKey)){
                    List<DistOverseasIpShare> distOverseasIpShareList = distOverseasIpShareService.getByDistNoAndWorkUniqueKey(distNo, matchWorkUniqueKey);
                    if(CollectionUtils.isEmpty(distOverseasIpShareList)){
                        continue;
                    }

                    workData.setWorkNo(matchWorkUniqueKey);
                    // 设置匹配作品标题（使用原始标题作为默认值）
                    workData.setWorkTitle(mapping.getOriginalTitle());
                    List<WorkIpRoy> workIpRoyList = new ArrayList<>();
                    workData.setWorkIpRoyList(workIpRoyList);
//                    matchWorkDataList.add(matchWorkData);
                    for(DistOverseasIpShare distOverseasIpShare : distOverseasIpShareList){
                        WorkIpRoy workIpRoy = new WorkIpRoy();
                        workIpRoy.setIpName(distOverseasIpShare.getIpName());
                        workIpRoy.setIpNameNo(distOverseasIpShare.getIpNameNo());
                        workIpRoy.setSociety(distOverseasIpShare.getIpSoc());
                        workIpRoy.setShare(distOverseasIpShare.getWorkIpShare().toString() + "%");
                        workIpRoy.setStatus(""); // 设置默认状态
                        workIpRoy.setTv("$0.00");
                        workIpRoy.setRadio("$0.00");
                        workIpRoy.setConcert("$0.00");
                        workIpRoy.setKaraoke("$0.00");
                        workIpRoy.setAirline("$0.00");
                        workIpRoy.setOthers("$0.00");
                        workIpRoy.setTotal("$0.00");
                        workIpRoyList.add(workIpRoy);
                    }
                }

            }

            distReportFieData.setWorkData(workDataList);


            Summary summary = new Summary();
            // 设置作品总数
            summary.setTotalWorks(workDataList.size());

            // 设置各类型金额总计（目前主要是others类型）
            String othersTotal = bigDecimaltoString(distCalcRetain.getSubTotalRoy());
            summary.setOthersTotal(othersTotal);
            summary.setTvTotal("$0.00");
            summary.setRadioTotal("$0.00");
            summary.setConcertTotal("$0.00");
            summary.setKaraokeTotal("$0.00");
            summary.setAirlineTotal("$0.00");

            // 设置总计格式
            summary.setTotalFormat(othersTotal);

            distReportFieData.setSummary(summary);
            reportFieDataList.add(distReportFieData);
        }


        reportFieDataList.stream().forEach(data -> {
            if (data.getWorkData().size() > 0) {
                List<JasperParam> jaspers = new ArrayList<>();
                List<FieWorkData> WorkData = data.getWorkData();
                Map<String, Object> param = new HashMap<>();
                DistReportFieData.Header header = data.getHeader();
                param.put("distributionNo", header.getDistributionNo());
                param.put("date", DateParse.format(new Date()));
                param.put("mustMemberName", header.getMustMemberName());
                param.put("ipBaseNo", header.getIpBaseNo());
                param.put("paNameNo", header.getPaNameNo());

                Summary summary = data.getSummary();
                param.put("totalWorks", String.valueOf(summary.getTotalWorks()));
                param.put("total", summary.getTotalFormat());
                param.put("tvTotal", summary.getTvTotal());
                param.put("radioTotal", summary.getRadioTotal());
                param.put("concertTotal", summary.getConcertTotal());
                param.put("karaokeTotal", summary.getKaraokeTotal());
                param.put("airlineTotal", summary.getAirlineTotal());
                param.put("otherTotal", summary.getOthersTotal());

                // 添加缺失的主模板参数
                param.put("SUBREPORT_DIR", "/ireport/jrxml/royaltyDetails/");
                param.put("allGeneralTotal", "$0.00");  // 所有一般类型总计
                param.put("allOthersTotal", summary.getOthersTotal());  // 所有其他类型总计
                param.put("allTotal", summary.getTotalFormat());  // 所有类型总计
                jaspers.add(new JasperParam(param, JSON.toJSONString(WorkData).getBytes()));
                String outFile = String.format("%s/%s/Roy_Dtl/%s_FIE_%s.pdf", outParentFile, distNo, header.getMustMemberName(), distNo);
                String filePath = "/ireport/jrxml/royaltyDetails/OverSeasFie_main.jrxml";
                try {
                    PdfReport.exprtPdf(outFile, jaspers, filePath);
                } catch (Exception e) {
                    log.error("distNo:【{}】，相关PDF报表生成失敗！", distNo, e);
                }
            }
        });
    }

    /**
     * 导出PDF，MOverSeas个人
     * @param distReportPAndSocietyData 个人数据
     * @param outParentFile 输出文件路径
     * @param distNo 分配代号
     * @param fileName 文件名
     */
    public void generateMOverSeasPDF(List<DistReportPAndIPData> distReportPAndSocietyData,String outParentFile, String distNo,String fileName) {
        log.info("开始生成MOverSeas的PDF，distNo：{}，fileName：{}，数据条数：{}", distNo, fileName, distReportPAndSocietyData.size());

        if (CollectionUtils.isEmpty(distReportPAndSocietyData)) {
            log.warn("MOverSeas PDF生成失败：distReportPAndSocietyData为空，distNo：{}", distNo);
            return;
        }

        try{
            String filePath = "/ireport/jrxml/royaltyDetails/" + fileName;
            log.info("使用模板文件路径：{}", filePath);

            distReportPAndSocietyData.stream().forEach(data -> {
                log.info("处理个人数据，ipBaseNo：{}，WorkData数量：{}",
                    data.getHeader() != null ? data.getHeader().getIpBaseNo() : "null",
                    data.getWorkData() != null ? data.getWorkData().size() : 0);

                if(data.getWorkData() != null && data.getWorkData().size() > 0){
                    List<JasperParam> jaspers = new ArrayList<>();
                    //先获取workData列表
                    List<WorkData> workDataList = data.getWorkData();
                    log.info("开始转换WorkData，原始WorkData数量：{}", workDataList.size());

                    // 创建一个新的 WorkData4MOverSeas 列表
                    List<WorkData4MOverSeas> workData4MOverSeasList = new ArrayList<>();

                    // 遍历 WorkData 列表并将每个对象转换为 WorkData4MOverSeas 对象
                    for (int i = 0; i < workDataList.size(); i++) {
                        WorkData workData = workDataList.get(i);
                        log.info("处理第{}个WorkData，workTitle：{}，workNo：{}",
                            i + 1, workData.getWorkTitle(), workData.getWorkNo());

                        WorkData4MOverSeas workData4MOverSeas = new WorkData4MOverSeas();

                        // 获取ipBaseNo和对应的paName
                        String ipBaseNo = data.getHeader().getIpBaseNo();
                        log.info("获取ipBaseNo：{}", ipBaseNo);

                        MbrIpName paName = mbrIpNameService.getPANameByIpBaseNo(ipBaseNo);
                        if (paName == null) {
                            log.warn("未找到ipBaseNo对应的paName：{}", ipBaseNo);
                            continue;
                        }
                        log.info("找到paName：{}，ipNameNo：{}", paName.getName(), paName.getIpNameNo());

                        // 筛选出与paName匹配的WorkIpRoy对象
                        List<WorkIpRoy> allWorkIpRoys = workData.getWorkIpRoyList();
                        log.info("WorkIpRoyList总数：{}", allWorkIpRoys != null ? allWorkIpRoys.size() : 0);

                        if (allWorkIpRoys != null) {
                            for (int j = 0; j < allWorkIpRoys.size(); j++) {
                                WorkIpRoy roy = allWorkIpRoys.get(j);
                                log.info("WorkIpRoy[{}]：ipNameNo={}，status={}，total={}",
                                    j, roy.getIpNameNo(), roy.getStatus(), roy.getTotal());
                            }
                        }

                        List<WorkIpRoy> matchedWorkIpRoys = workData.getWorkIpRoyList().stream()
                                .filter(workIpRoy ->
                                        workIpRoy != null && workIpRoy.getIpNameNo() != null &&
                                        workIpRoy.getIpNameNo().equals(paName.getIpNameNo()))
                                .collect(Collectors.toList());

                        log.info("匹配的WorkIpRoy数量：{}", matchedWorkIpRoys.size());

                        // 获取第一个 WorkIpRoy 对象
                        if(!matchedWorkIpRoys.isEmpty()){
                            log.info("找到匹配的WorkIpRoy，开始设置WorkData4MOverSeas属性");

                            WorkIpRoy firstMatchedWorkIpRoy = matchedWorkIpRoys.get(0);

                            // 设置 WorkData4MOverSeas 对象的属性
                            workData4MOverSeas.setWorkTitle(workData.getWorkTitle());
                            workData4MOverSeas.setWorkNo(workData.getWorkNo());
                            workData4MOverSeas.setOriginaltitle(workData.getOriginaltitle());
                            workData4MOverSeas.setWorkIpRoyList(workData.getWorkIpRoyList());

                            // 从第一个 WorkIpRoy 对象中设置相关字段
                            workData4MOverSeas.setIpNameNo(firstMatchedWorkIpRoy.getIpNameNo());
                            workData4MOverSeas.setStatus(firstMatchedWorkIpRoy.getStatus());
                            workData4MOverSeas.setSociety(firstMatchedWorkIpRoy.getSociety());
                            workData4MOverSeas.setTv(firstMatchedWorkIpRoy.getTv());
                            workData4MOverSeas.setRadio(firstMatchedWorkIpRoy.getRadio());
                            workData4MOverSeas.setConcert(firstMatchedWorkIpRoy.getConcert());
                            workData4MOverSeas.setKaraoke(firstMatchedWorkIpRoy.getKaraoke());
                            workData4MOverSeas.setGeneral(firstMatchedWorkIpRoy.getAirline());
                            workData4MOverSeas.setOthers(firstMatchedWorkIpRoy.getOthers());
                            workData4MOverSeas.setWorkTotal(firstMatchedWorkIpRoy.getTotal());

                            log.info("成功设置WorkData4MOverSeas属性：workTitle={}，workTotal={}，tv={}，radio={}，concert={}，karaoke={}，general={}，others={}",
                                workData4MOverSeas.getWorkTitle(), workData4MOverSeas.getWorkTotal(),
                                workData4MOverSeas.getTv(), workData4MOverSeas.getRadio(),
                                workData4MOverSeas.getConcert(), workData4MOverSeas.getKaraoke(),
                                workData4MOverSeas.getGeneral(), workData4MOverSeas.getOthers());

                            // 将新创建的对象加入列表
                            workData4MOverSeasList.add(workData4MOverSeas);
                        } else {
                            log.warn("未找到匹配的WorkIpRoy，跳过当前WorkData，workTitle：{}", workData.getWorkTitle());
                        }
                    }

                    log.info("WorkData转换完成，最终WorkData4MOverSeas数量：{}", workData4MOverSeasList.size());

                    if (workData4MOverSeasList.isEmpty()) {
                        log.warn("WorkData4MOverSeas列表为空，跳过PDF生成，ipBaseNo：{}", data.getHeader().getIpBaseNo());
                        return;
                    }

                    Map<String,Object> param = new HashMap<>();
                    DistReportPAndIPData.Header header = data.getHeader();
                    param.put("distributionNo", header.getDistributionNo());
                    param.put("date", DateParse.format(new Date()));
                    param.put("mustMemberName", header.getMustMemberName());
                    param.put("ipBaseNo", header.getIpBaseNo());
                    param.put("paNameNo", header.getPaNameNo());

                    // 从workData4MOverSeasList计算汇总值
                    int totalWorks = workData4MOverSeasList.size();
                    BigDecimal tvTotalAmount = BigDecimal.ZERO;
                    BigDecimal radioTotalAmount = BigDecimal.ZERO;
                    BigDecimal concertTotalAmount = BigDecimal.ZERO;
                    BigDecimal karaokeTotalAmount = BigDecimal.ZERO;
                    BigDecimal generalTotalAmount = BigDecimal.ZERO; // 对应airlineTotal
                    BigDecimal othersTotalAmount = BigDecimal.ZERO;
                    BigDecimal grandTotal = BigDecimal.ZERO;

                    // 遍历workData4MOverSeasList计算各项汇总
                    for (WorkData4MOverSeas workData4MOverSeas : workData4MOverSeasList) {
                        // 解析各项金额（去掉$符号并转换为BigDecimal）
                        tvTotalAmount = tvTotalAmount.add(parseAmount(workData4MOverSeas.getTv()));
                        radioTotalAmount = radioTotalAmount.add(parseAmount(workData4MOverSeas.getRadio()));
                        concertTotalAmount = concertTotalAmount.add(parseAmount(workData4MOverSeas.getConcert()));
                        karaokeTotalAmount = karaokeTotalAmount.add(parseAmount(workData4MOverSeas.getKaraoke()));
                        generalTotalAmount = generalTotalAmount.add(parseAmount(workData4MOverSeas.getGeneral()));
                        othersTotalAmount = othersTotalAmount.add(parseAmount(workData4MOverSeas.getOthers()));
                        grandTotal = grandTotal.add(parseAmount(workData4MOverSeas.getWorkTotal()));
                    }

                    log.info("计算汇总值完成：totalWorks={}，tvTotal={}，radioTotal={}，concertTotal={}，karaokeTotal={}，generalTotal={}，othersTotal={}，grandTotal={}",
                        totalWorks, tvTotalAmount, radioTotalAmount, concertTotalAmount, karaokeTotalAmount, generalTotalAmount, othersTotalAmount, grandTotal);

                    param.put("totalWorks", String.valueOf(totalWorks));
                    param.put("total", formatAmount(grandTotal));
                    param.put("tvTotal", formatAmount(tvTotalAmount));
                    param.put("radioTotal", formatAmount(radioTotalAmount));
                    param.put("concertTotal", formatAmount(concertTotalAmount));
                    param.put("karaokeTotal", formatAmount(karaokeTotalAmount));
                    param.put("airlineTotal", formatAmount(generalTotalAmount));
                    param.put("otherTotal", formatAmount(othersTotalAmount));

                    log.info("设置PDF参数完成，参数信息：distributionNo={}，mustMemberName={}，ipBaseNo={}，paNameNo={}，totalWorks={}，total={}",
                        header.getDistributionNo(), header.getMustMemberName(), header.getIpBaseNo(),
                        header.getPaNameNo(), totalWorks, formatAmount(grandTotal));

                    // 将数据转换为JSON字符串
                    String jsonData = JSON.toJSONString(workData4MOverSeasList);
                    log.info("JSON数据长度：{}，前200个字符：{}", jsonData.length(),
                        jsonData.length() > 200 ? jsonData.substring(0, 200) + "..." : jsonData);

                    jaspers.add(new JasperParam(param, jsonData.getBytes()));
                    String outFile = String.format("%s/%s/Roy_Dtl/%s_%s_%s.pdf", outParentFile, distNo,header.getMemberNo(),header.getIpBaseNo(), distNo);
                    log.info("准备生成PDF文件：{}", outFile);

                    try {
                        PdfReport.exprtPdf(outFile, jaspers, filePath);
                        log.info("PDF文件生成成功：{}", outFile);
                    } catch (Exception e) {
                        log.error("distNo:【{}】，PDF报表生成失败！outFile：{}，错误信息：{}", distNo, outFile, e.getMessage(), e);
                    }
                } else {
                    log.warn("跳过空的WorkData数据，ipBaseNo：{}", data.getHeader() != null ? data.getHeader().getIpBaseNo() : "null");
                }
            });

        }catch (Exception e){
            log.error("生成MOverSeas PDF过程中发生异常，distNo：{}，错误信息：{}", distNo, e.getMessage(), e);
        }

        log.info("MOverSeas PDF生成流程结束，distNo：{}", distNo);
    }

    /**
     * 解析金额字符串，去掉$符号并转换为BigDecimal
     * @param amountStr 金额字符串，如"$123.45"或"123.45"
     * @return BigDecimal金额，如果解析失败返回0
     */
    private BigDecimal parseAmount(String amountStr) {
        if (StringUtils.isBlank(amountStr)) {
            return BigDecimal.ZERO;
        }

        try {
            // 去掉$符号和逗号
            String cleanAmount = amountStr.replace("$", "").replace(",", "").trim();
            if (StringUtils.isBlank(cleanAmount) || "0".equals(cleanAmount)) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(cleanAmount);
        } catch (NumberFormatException e) {
            log.warn("解析金额失败：{}，返回0", amountStr);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 格式化金额为字符串
     * @param amount BigDecimal金额
     * @return 格式化后的金额字符串，如"$123.45"
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return "$0";
        }
        return "$" + amount.setScale(2, RoundingMode.HALF_UP).toString();
    }

    public DistReportCategoryRoyalites generateDistReportCategoryRoyalites(){
        DistReportCategoryRoyalites royalites = new DistReportCategoryRoyalites();
        royalites.init();

        royalites.setUpaAmtSum(BigDecimal.ZERO);
        royalites.setSmaAmtSum(BigDecimal.ZERO);
        royalites.setSnDistRoySum(BigDecimal.ZERO);
        royalites.setLocalWsPtSum(BigDecimal.ZERO);
        royalites.setLocalWsDistRoySum(BigDecimal.ZERO);
        royalites.setAdjPt1ValueSum(BigDecimal.ZERO);
        royalites.setAdjPt2ValueSum(BigDecimal.ZERO);
        royalites.setAdjPt3ValueSum(BigDecimal.ZERO);
        royalites.setRetRoyAmtSum(BigDecimal.ZERO);

        return royalites;

    }

    /**
     * O分配 Overseas Checking #7 FID without detail data
     * @param outParentFile
     * @param distNo
     */
    public void generateWithoutDetailData(String outParentFile, String distNo) {

        XxlJobLogger.log("generateWithoutDetailData start,distNo:{}",distNo);

        List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distNo);

        List<Long> receiptIds = distParamOverseasList.stream().map(di -> di.getReceiptId()).distinct().collect(Collectors.toList());

        List<ListOverseasReceipt> receiptList = listOverseasReceiptService.listByIds(receiptIds);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(receiptList)){
            return ;
        }

//        List<Long> detailIds = distParamOverseasList.stream().map(di -> di.getReceiptDetailsId()).distinct().collect(Collectors.toList());

        List<ListOverseasReceiptDetails> detailList = listOverseasReceiptDetailsService.getListOverseasReceiptDetailsByReceiptIds(receiptIds);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(detailList)){
            return;
        }

        Map<Long,ListOverseasReceiptDetails> detailsMap = detailList.stream().collect(Collectors.toMap(ListOverseasReceiptDetails :: getId, Function.identity(), (a, b) -> a));

        List<ListOverseasFileBase> baseList = listOverseasFileBaseService.getListOverseasFileBaseByReceiptIds(receiptIds);

        List<ListOverseasWithoutDetail> exportList = new ArrayList<>();

        baseList.forEach(b -> {
            if(b.getFileTotalAmount() == null || BigDecimal.ZERO.compareTo(b.getFileTotalAmount()) == 0){
                ListOverseasWithoutDetail temp = new ListOverseasWithoutDetail();
                temp.setFileBaseId(b.getId());
                temp.setRemitSociety(b.getRemitSocietyCode());
                temp.setFileType(b.getFileType());
                temp.setFileName(b.getFileName()) ;
                temp.setReceiptId(b.getReceiptId());

                ListOverseasReceiptDetails detail = detailsMap.get(b.getReceiptDetailsId());
                if(detail != null){
                    temp.setDistNo(detail.getDistNo());
                    temp.setDistOrderNumber(detail.getDistOrderNumber());
                }

                temp.init();
                exportList.add(temp);
            }
        });

        String[] title = new String[]{"Remit Soc","水單ID","overseas_dist_seq","Dist_no","FID","Format_type","Filename"};
        String[] key = new String[]{"remitSociety","receiptId","distOrderNumber","distNo","fileBaseId","fileType","fileName"};
        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s%sFID without detail data_%s.xlsx", outParentFile, File.separator, distNo);
        GenerateExcelUtil<ListOverseasWithoutDetail> generateExcelWriter = null;
        Workbook workbook = null;
        try {
            generateExcelWriter = new GenerateExcelUtil<>(title, key, exportList);
            generateExcelWriter.init();
            workbook = generateExcelWriter.getWorkbook();
            FileOutputStream fileOutputStream = new FileOutputStream(filePath);
            workbook.write(fileOutputStream);
            XxlJobLogger.log("distNo{}：文件生成成功！ ",distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("workbook.close() error",e);
            }
        }

    }

    /**
     * O分配 Overseas Checking #1&#2&&8 Roy is different from file head
     * @param outParentFile
     * @param distNo
     *
     * 1.Detail Amount Sum與Footer Amount Total金額不相等
     * 2.Dist ip Amount Sum與Detail Amount Sum金額不相等
     *
     *  Detail Amount Sum = E4檔中除了第1行(HEADER)，最後一行(TRAILER)及FIE (REC_TYPE=3)之金額加總
     *  目前系统先设置 ListOverseasFileBase.totalAmount = nomalAmount + fieAmount + adjAmount，
     *  解析尾部的时候又设置 totalAmount = footerAMount,最终totalAmount = footerAMount。这个地方感觉可以不设置 totalAmount = nomalAmount + fieAmount + adjAmount （20230406）
     *
     *  Footer Amount Total =  ListOverseasFileBase.totalAmount
     *
     *  Detail Amount Sum = = nomalAmount + adjAmount
     *
     *  Dist ip Amount Sum 分配計算後每個IP分配到的金額加總
     */
    public void generateAmountCheck(String outParentFile, String distNo) {

        XxlJobLogger.log("generateAmountCheck start,distNo:{}",distNo);

        List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distNo);

        List<Long> receiptIds = distParamOverseasList.stream().map(di -> di.getReceiptId()).distinct().collect(Collectors.toList());

        List<ListOverseasReceipt> receiptList = listOverseasReceiptService.listByIds(receiptIds);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(receiptList)){
            return ;
        }

//        List<Long> detailIds = distParamOverseasList.stream().map(di -> di.getReceiptDetailsId()).distinct().collect(Collectors.toList());

        List<ListOverseasReceiptDetails> detailList = listOverseasReceiptDetailsService.getListOverseasReceiptDetailsByReceiptIds(receiptIds);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(detailList)){
            return;
        }

        Map<Long,ListOverseasReceiptDetails> detailsMap = detailList.stream().collect(Collectors.toMap(ListOverseasReceiptDetails :: getReceiptId, Function.identity(), (a, b) -> a));

        List<ListOverseasFileBase> baseList = listOverseasFileBaseService.getListOverseasFileBaseByReceiptIds(receiptIds);
        List<Long> fileBaseIds = baseList.stream().map(b -> b.getId()).distinct().collect(Collectors.toList());

        Map<Long,BigDecimal> amountMap = listMatchDataOverseasMappingService.getAmountGroupByFid(fileBaseIds);

        List<Map<String,String>> exportList = new ArrayList<>();

        String[] title = new String[]{"Remit Soc","水單ID","overseas_dist_seq","FID","Format_type","Footer Amount Total","Detail Amount Sum","Dist ip Amount Sum","error_msg"};
        String[] key = new String[]{"remitSociety","receiptId","distOrderNumber","fileBaseId","fileType","footerAmount","detailTotalAmount","ipTotalAmount","errorMsg"};
        String[] errorMsg = new String[]{"Detail Amount Sum與Footer Amount Total金額不相等","Dist ip Amount Sum與Detail Amount Sum金額不相等"} ;
        baseList.forEach(b -> {
            if(b.getNomalAmount() == null){
                b.setNetAmount(BigDecimal.ZERO);
            }
            if(b.getAdjAmount() == null){
                b.setAdjAmount(BigDecimal.ZERO);
            }
            BigDecimal detailAmount = b.getNomalAmount().add(b.getAdjAmount());
            BigDecimal footerAmount = b.getTotalAmount() == null ? BigDecimal.ZERO : b.getTotalAmount();
            BigDecimal ipTotalAmount = amountMap.get(b.getId());
            if(ipTotalAmount == null){
                ipTotalAmount = BigDecimal.ZERO;
            }

            ListOverseasReceiptDetails receiptDetails = detailsMap.get(b.getReceiptId()) ;
            if(receiptDetails == null){
                return;
            }

            if(detailAmount.compareTo(footerAmount) != 0){ // 明细金额不等于foorerAmount
                Map<String,String> map = new HashMap<>();
                map.put(key[0],b.getRemitSocietyCode() + "");
                map.put(key[1],b.getReceiptId() + "");
                if( receiptDetails != null){
                    map.put(key[2],receiptDetails.getDistOrderNumber() + "");
                }
                map.put(key[3],b.getId() + "");
                map.put(key[4],b.getFileType());
                map.put(key[5],footerAmount.toString());
                map.put(key[6],detailAmount.toString());
                map.put(key[7],ipTotalAmount.toString());
                map.put(key[8],errorMsg[0]);
                exportList.add(map);
            }

            if(detailAmount.compareTo(ipTotalAmount) != 0){ // 明细金额不等于分配金额
                Map<String,String> map = new HashMap<>();
                map.put(key[0],b.getRemitSocietyCode() + "");
                map.put(key[1],b.getReceiptId() + "");
                if( receiptDetails != null){
                    map.put(key[2],receiptDetails.getDistOrderNumber() + "");
                }
                map.put(key[3],b.getId() + "");
                map.put(key[4],b.getFileType());
                map.put(key[5],footerAmount.toString());
                map.put(key[6],detailAmount.toString());
                map.put(key[7],ipTotalAmount.toString());
                map.put(key[8],errorMsg[1]);
                exportList.add(map);
            }

        });

        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s%sRoy is different from file head_%s.xlsx", outParentFile, File.separator, distNo);
        GenerateExcelUtil<Map<String,String>> generateExcelUtil = null;
        Workbook workbook = null;
        try {
            generateExcelUtil = new GenerateExcelUtil<>(title, key, exportList);
            generateExcelUtil.init();
            workbook = generateExcelUtil.getWorkbook();
            FileOutputStream fileOutputStream = new FileOutputStream(filePath);
            workbook.write(fileOutputStream);
            log.info("generateIpNotInWork end,distNo:{}",distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            // 关闭GenerateCsvWriter
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("workbook.close() error",e);
            }
        }

    }

    /**
     *
     * @param outParentFile
     * @param distNo
     *
     * list_match_data_overseas_mapping.reject_code = F02  比對到的ip，不在match到的work裡面的ip
     */
    public void generateIpNotInWork(String outParentFile, String distNo){
        XxlJobLogger.log("generateIpNotInWork start,distNo:{}",distNo);

        // 根据distNo获取DistParamOverseas列表
        List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distNo);

        // 获取receiptId列表
        List<Long> receiptIds = distParamOverseasList.stream().map(di -> di.getReceiptId()).distinct().collect(Collectors.toList());

        // 根据receiptId列表获取ListOverseasReceipt列表
        List<ListOverseasReceipt> receiptList = listOverseasReceiptService.listByIds(receiptIds);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(receiptList)){
            return ;
        }

//        List<Long> detailIds = distParamOverseasList.stream().map(di -> di.getReceiptDetailsId()).distinct().collect(Collectors.toList());

        // 根据receiptId列表获取ListOverseasReceiptDetails列表
        List<ListOverseasReceiptDetails> detailList = listOverseasReceiptDetailsService.getListOverseasReceiptDetailsByReceiptIds(receiptIds);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(detailList)){
            return;
        }

        // 根据receiptId列表获取ListOverseasFileBase列表
        List<ListOverseasFileBase> baseList = listOverseasFileBaseService.getListOverseasFileBaseByReceiptIds(receiptIds);
        List<Long> fids = baseList.stream().map(b -> b.getId()).collect(Collectors.toList());

        // 查询not_in_work=1的记录，表示IP不在作品中
        List<DistOverseasCheckingIpNotInWork> exportList = listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByFidsAndNotInWork(fids, 1);

        // 定义表头和字段
        String[] title = new String[]{"Dist No","Adj No","overseas_dist_seq","Pool Code","source_work_code","original_title","ip_name_no","pa_name_no","Name",
                "Work Ip Role","Society Code","Ip Share","Fie Code","Dist Ip Share","Amount Seq No","Distinct Worknum","Match_Work_id","Match_Worknum_Society_code"};
        String[] key = new String[]{"distNo","adjNo","overseasDistSeq","poolCode","sourceWorkCode","originalTitle","ipNameNo","paNameNo","name","workIpRole",
                "societyCode","ipShare","fieCode","distIpShare","amountSeqNo","distinctWorknum","matchWorkId","matchWorknumSocietyCode"};


        // 判断操作系统，如果是Windows，则设置输出路径
        if (CommonUtils.isWindows()) {
            outParentFile = "D:\\Test";
        }
        // 设置输出文件路径
        String filePath = String.format("%s%sIP NOT IN WORK_%s.xlsx", outParentFile, File.separator, distNo);
        GenerateExcelUtil<DistOverseasCheckingIpNotInWork> generateExcelUtil = null;
        Workbook workbook = null;
        try {
            // 初始化GenerateCsvWriter
            generateExcelUtil = new GenerateExcelUtil<>(title, key, exportList);
            generateExcelUtil.init();
            workbook = generateExcelUtil.getWorkbook();
            FileOutputStream fileOutputStream = new FileOutputStream(filePath);
            workbook.write(fileOutputStream);
            log.info("generateIpNotInWork end,distNo:{}",distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            // 关闭GenerateCsvWriter
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("workbook.close() error",e);
            }
        }
    }

    public void generateOverseasDistributionPaymentInformation(String outParentFile, String distNo){

        List<OverseasDistributionPaymentInfomation> paymentInfomationList = distReportService.reportOverseasDistributionPaymentInfomation(distNo);

        List<OverseasDistributionPaymentInfomation> not0 = new ArrayList<>();
        paymentInfomationList.forEach(p -> {
            BigDecimal amount = p.getNetRoy().subtract(p.getFieAmount()).subtract(p.getRetainRoy()).subtract(p.getSdRoy()).subtract(p.getMustMemberRoy()) ;
            if(amount.compareTo(BigDecimal.ZERO) != 0){
                not0.add(p) ;
            }
        });

        String[] check_title = new String[]{"Dist No","overseas_dist_seq","Period","Society Name","Net Roy","FIE","Retain Roy","Retain percent Roy","Sd Roy","Must Member Roy"};
        String[] check_key = new String[]{"distNo","overseasDistSeq","period","societyName","netRoy","fieAmount","retainRoy","retainRoyRate","sdRoy","mustMemberRoy"};

        String[] info_title = new String[]{"Dist No","overseas_dist_seq","Period","Society Name","Net Roy","Non Member Roy","Retain Roy","Sd Roy","Must Member Roy"};
        String[] info_key = new String[]{"distNo","overseasDistSeq","period","societyName","netRoy","nonMemberRoy","retainRoy","sdRoy","mustMemberRoy"};


        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String check_filePath = String.format("%s%sPayment information checking_%s.xlsx", outParentFile, File.separator, distNo);
        String info_filePath = String.format("%s%sOverseas Distribution Payment Information_%s.xlsx", outParentFile, File.separator, distNo);
        GenerateExcelUtil<OverseasDistributionPaymentInfomation> generateExcelUtil_check = null;
        GenerateExcelUtil<OverseasDistributionPaymentInfomation> generateExcelUtil_info = null;
        Workbook workbook_check = null;
        Workbook workbook_info = null;
        try {

            generateExcelUtil_check = new GenerateExcelUtil<>(check_title, check_key, not0);
            generateExcelUtil_check.init();
            workbook_check = generateExcelUtil_check.getWorkbook();
            FileOutputStream fileOutputStream = new FileOutputStream(check_filePath);
            workbook_check.write(fileOutputStream);

            generateExcelUtil_info = new GenerateExcelUtil<>(info_title, info_key, not0);
            generateExcelUtil_info.initTitle();
            workbook_info = generateExcelUtil_info.getWorkbook();
            fileOutputStream = new FileOutputStream(info_filePath);
            workbook_check.write(fileOutputStream);
            log.info("generateOverseasWorkDetails end,distNo:{}",distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            try {
                workbook_check.close();
                workbook_info.close();
            } catch (IOException e) {
                log.error("workbook.close() error",e);
            }
        }
    }

    public void generateOverseasWorkDetails(String outParentFile, String distNo){
        XxlJobLogger.log("generateOverseasWorkDetails start,distNo:{}",distNo);
        List<OverseasDistributionPaymentWorkDetail> details = distReportService.reportOverseasDistributionPaymentWorkDetail(distNo);

        List<String> workUniqueKeys = details.stream().map(d -> d.getWorkUniqueKey()).distinct().collect(Collectors.toList());

        List<WrkIswc> iswcList = wrkIswcService.getWrkIswcByWorkUniqueKeys(workUniqueKeys);

        Map<String,String> iswcMap = iswcList.stream().filter(i -> !StringUtils.equals(i.getIswc(),"")).collect(Collectors.toMap(WrkIswc::getWorkUniqueKey, WrkIswc::getIswc, (a,b) -> a));

        for(OverseasDistributionPaymentWorkDetail detail : details){
            detail.setDistNo(distNo);
            if(iswcMap.containsKey(detail.getWorkUniqueKey())){
                String workCode = iswcMap.get(detail.getWorkUniqueKey());
                workCode = workCode.substring(0,1) + "-" + workCode.substring(1,11);
                detail.setWorkCode(workCode);
            } else {
                String[] workCodes = detail.getWorkUniqueKey().split("-");
                String workSocietyCode = workCodes[0];
                if(Constants.socInitMap.containsKey(workSocietyCode)){
                    detail.setWorkCode(Constants.socInitMap.get(workSocietyCode) + "-" + workCodes[1]);
                } else {
                    detail.setWorkCode(detail.getWorkUniqueKey());
                }
            }
        }

        String[] title = new String[]{"Dist No","Account Id","Account Name","Society Code","overseas_dist_seq","Work Code","Work Title","Royalties"};
        String[] key = new String[]{"distNo","accountId","accountName","societyCode","adjDistNo","workCode","workTitle","royalties"};

        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s%s%s 6. Work Detail- Overseas Distribution Payment Work Detail.xlsx", outParentFile, File.separator, distNo);
        GenerateExcelUtil<OverseasDistributionPaymentWorkDetail> generateExcelUtil;
        Workbook workbook = null;
        try {
            // 初始化GenerateCsvWriter
            generateExcelUtil = new GenerateExcelUtil<>(title, key, details);
            generateExcelUtil.init();
            workbook = generateExcelUtil.getWorkbook();
            FileOutputStream fileOutputStream = new FileOutputStream(filePath);
            workbook.write(fileOutputStream);
            log.info("generateOverseasWorkDetails end,distNo:{}",distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("distNo:【{}】，文件關閉失敗！", distNo,e);
            }
        }
    }

    public void generateCrossCheck(String outParentFile, String distNo) {
        XxlJobLogger.log("generateCrossCheckReport start, distNo: {}", distNo);

        try {

            List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distNo);
            if(CollectionUtils.isEmpty(distParamOverseasList)){
                XxlJobLogger.log("generateCrossCheckReport no data, distNo: {}", distNo);
                return;
            }

            Map<Long,String> osdDistParamOverseasMap = new HashMap<>();
            Map<String,String> specialMap = new HashMap<>();
            List<String> otherTitleList = new ArrayList<>();
            for(DistParamOverseas distParamOverseas : distParamOverseasList){
                if(StringUtils.isBlank(distParamOverseas.getCategoryCode())){
                    if(!osdDistParamOverseasMap.containsKey(distParamOverseas.getReceiptId())){
                        Integer distNumber = distParamOverseas.getDistOrderNumber();
                        String format = "%s_%s%s";
                        if(distParamOverseas.getSourceSocietyCode() < 100){
                            format = "0%s_0%s%s";
                        }
                        String title = String.format(format, distParamOverseas.getSourceSocietyCode(),distParamOverseas.getSourceSocietyCode(),distNumber);
                        osdDistParamOverseasMap.put(distParamOverseas.getReceiptId(),title);
                        otherTitleList.add(title);
                    }
                } else {
                    specialMap.put(distParamOverseas.getCategoryCode(),distParamOverseas.getSourceSocietyName());
                    String title = String.format("%s_%s", distParamOverseas.getSourceSocietyName(),distParamOverseas.getSourceSocietyName());
                    specialMap.put(distParamOverseas.getCategoryCode(),title);
                    otherTitleList.add(title);
                }
            }

            List<ListOverseasFileBase> listOverseasFileBaseList = listOverseasFileBaseService.getListOverseasFileBaseByReceiptIds(new ArrayList<>(osdDistParamOverseasMap.keySet()));
            Map<Long,String> fileBaseIdRoyNoMap = listOverseasFileBaseList.stream().collect(Collectors.toMap(ListOverseasFileBase::getId, v-> osdDistParamOverseasMap.get(v.getReceiptId())));

            List<DistCalcRetain> distCalcRetainList = distCalcRetainService.listWithDist(distNo);
            if (CollectionUtils.isEmpty(distCalcRetainList)) {
                XxlJobLogger.log("generateCrossCheckReport no retain data, distNo: {}", distNo);
                return;
            }


            //共有标题
            List<String> titleList = new ArrayList<>(Arrays.asList("Expr1", "Expr2", "Expr3", "合計 Royalties"));

            distCalcRetainList.stream().sorted(Comparator.comparing(DistCalcRetain::getIpBaseNo));
            Map<Long,Map<String, Object> > crossMap = new HashMap<>();
            for (DistCalcRetain distCalcRetain : distCalcRetainList) {
                if(distCalcRetain.getSubTotalRoy().compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                Map<String,Object> map = new HashMap<>();
                crossMap.put(distCalcRetain.getId() ,map);

                map.put(titleList.get(0), distNo);
                map.put(titleList.get(1), distCalcRetain.getIpBaseNo());
                map.put(titleList.get(2), distCalcRetain.getIpChineseName());
                map.put(titleList.get(3), distCalcRetain.getSubTotalRoy());
            }

            for(DistCalcRetain distCalcRetain : distCalcRetainList){
                List<DistDataCalcWorkIpRoy> workIpRoyList = distDataCalcWorkIpRoyService.getByRetanId(distCalcRetain.getId());
                if (CollectionUtils.isEmpty(workIpRoyList)) {
                    continue;
                }
                Map<Long, BigDecimal> fileBaseAmountMap = workIpRoyList.stream()
                        .filter(workIpRoy -> StringUtils.equals("OSD", workIpRoy.getCategoryCode()))
                        .collect(Collectors.groupingBy(
                                DistDataCalcWorkIpRoy::getFileBaseId,
                                Collectors.reducing(BigDecimal.ZERO, DistDataCalcWorkIpRoy::getDistRoy, BigDecimal::add)
                        ));

                for(Map.Entry<Long, BigDecimal> entry : fileBaseAmountMap.entrySet()){
                    if(entry.getValue().compareTo(BigDecimal.ZERO) == 0){
                        continue;
                    }
                    String title = fileBaseIdRoyNoMap.get(entry.getKey());
                    Map<String,Object> map = crossMap.get(distCalcRetain.getId());
                    if(map == null){
                        continue;
                    }
                    map.put(title,entry.getValue());
                }

                Map<String, BigDecimal> categoryAmountMap = workIpRoyList.stream()
                        .filter(workIpRoy -> !StringUtils.equals(workIpRoy.getCategoryCode(),"OSD"))
                        .collect(Collectors.groupingBy(
                                DistDataCalcWorkIpRoy::getCategoryCode,
                                Collectors.reducing(BigDecimal.ZERO, DistDataCalcWorkIpRoy::getDistRoy, BigDecimal::add)
                        ));

                for (Map.Entry<String, BigDecimal> entry : categoryAmountMap.entrySet()) {
                    String title = specialMap.get(entry.getKey());
                    if(StringUtils.isBlank( title)){
                        title = entry.getKey();
                        otherTitleList.add( title);
                    }
                    Map<String,Object> map = crossMap.get(distCalcRetain.getId());
                    if(map == null){
                        continue;
                    }
                    map.put(title,entry.getValue());
                }
            }



            // 确定文件路径
            if (CommonUtils.isWindows()) {
                outParentFile = "D:\\Test";
            }
            String filePath = String.format("%s%scross_check_%s.xlsx", outParentFile, File.separator, distNo);

            titleList.addAll(otherTitleList);

            List<Map<String, Object>> dataList = crossMap.values().stream().collect(Collectors.toList());

            // 生成 CSV 文件
            GenerateExcelUtil<Map<String, Object>> generateExcelUtil;
            Workbook workbook = null;
            try {
                generateExcelUtil = new GenerateExcelUtil<>(titleList.toArray(new String[0]),titleList.toArray(new String[0]),dataList);
                generateExcelUtil.init();
                JSON.DEFFAULT_DATE_FORMAT = "yyyyMMdd";
                workbook = generateExcelUtil.getWorkbook();
                FileOutputStream fileOutputStream = new FileOutputStream(filePath);
                workbook.write(fileOutputStream);
                XxlJobLogger.log("generateCrossCheckReport end, distNo: {}", distNo);
            } catch (IOException e) {
                XxlJobLogger.log("distNo:【{}】，文件生成失败！", distNo, e);
                throw new IOException("文件生成失败: " + e);
            } finally {
                if (workbook != null) {
                    workbook.close();
                }
            }

        } catch (Exception e) {
            XxlJobLogger.log("generateCrossCheckReport error: {}", e);
//            throw new Exception(e.getMessage());

        }
    }

/*    public void generateCrossCheck(String outParentFile, String distNo){
        XxlJobLogger.log("generateCrossCheckReport start,distNo:{}",distNo);
        List<OverseasDistributionPaymentWorkDetail> details = distReportService.reportOverseasDistributionPaymentWorkDetail(distNo);
        Map<String,Map<String,BigDecimal>> crossCheck = new HashMap<>();
        Set<String> titleSet = new TreeSet<>();
        String totalRoyalties = "Total Royalties";
        for(OverseasDistributionPaymentWorkDetail detail : details){
            String accountId = detail.getAccountId();
            Map<String,BigDecimal> map =  crossCheck.get(accountId) ;
            String title = detail.getSocietyCode() + "-" + detail.getOverseasDistSeq() ;
            if(null == map){
                map = new HashMap<>();
                crossCheck.put(accountId,map);
                map.put(title,detail.getRoyalties());
                titleSet.add(title);
                map.put(totalRoyalties,detail.getRoyalties());
            } else if(map.containsKey(title)) {
                map.put(title,map.get(title).add(detail.getRoyalties()));
                map.put(totalRoyalties,map.get(totalRoyalties).add(detail.getRoyalties()));
            } else {
                map.put(title,detail.getRoyalties());
                titleSet.add(title);
                map.put(totalRoyalties,map.get(totalRoyalties).add(detail.getRoyalties()));
            }
        }

        List<String> titleList = Arrays.asList("Expr1","Expr2","Expr3","合計 Royalties");
        List<String> keyList = Arrays.asList("distNo","accountId","accountName",totalRoyalties);
        titleSet.forEach( t -> {
            titleList.add(t);
            keyList.add(t);
        });

        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s%scross_check_%s.csv", outParentFile, File.separator, distNo);
        GenerateCsvWriter<OverseasDistributionPaymentWorkDetail> generateCsvWriter = null;
        try {
            generateCsvWriter = new GenerateCsvWriter<>((String[]) titleList.toArray(), (String[]) keyList.toArray(), filePath);
            generateCsvWriter.initTitle();
            JSON.DEFFAULT_DATE_FORMAT = "yyyyMMdd";
            generateCsvWriter.writeList(details);
            log.info("generateCrossCheckReport end,distNo:{}",distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失敗！", distNo,e);
        } finally {
            generateCsvWriter.close();
        }

    }*/

    public void generatePcsv(String outParentFile, String distNo){
        XxlJobLogger.log("generatePcsv   start,distNo:{}",distNo);
        List<OverseasDistributionPaymentWorkDetail> details = distReportService.reportOverseasDistributionPaymentWorkDetail(distNo);

        String[] title = new String[]{"DIST_NO","WORK_CODE","WORK_TITLE","OT_WORK_CODE","OT_WORK_TITLE","IP_NAME_NO","NAME","SOCIETY_CODE","WORK_IP_ROLE","IP_SHARE","RADIO","TV","CONCERT","FILM","GENERAL","OTHERS"};
        String[] key = new String[]{"distNo","workCode","workTitle","otWorkCode","otWorkTitle","ipNameNo","name","societyCode","workIpRole","ipShare","radio","tv","concert","film","general","others"};

        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s%swork detail_%s.csv", outParentFile, File.separator, distNo);
        GenerateCsvWriter<OverseasDistributionPaymentWorkDetail> generateCsvWriter = null;
        try {
            generateCsvWriter = new GenerateCsvWriter<>(title, key, filePath);
            generateCsvWriter.initTitle();
            JSON.DEFFAULT_DATE_FORMAT = "yyyyMMdd";
            generateCsvWriter.writeList(details);
            XxlJobLogger.log("generateOverseasWorkDetails end,distNo:{}",distNo);
        } catch (IOException e) {
            XxlJobLogger.log("distNo:【{}】，文件生成失敗！", distNo);
            XxlJobLogger.log(e);
        } finally {
            generateCsvWriter.close();
        }
    }

    public String bigDecimaltoString(BigDecimal bigDecimal){
        if(bigDecimal == null){
            return null;
        }

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        try{
            String formattedNumber = numberFormat.format(bigDecimal);
            return formattedNumber;
        }catch (Exception e){
            e.printStackTrace();
        }

        return "0" ;
    }


    /*public void generateOverseasWorkDetails(String outParentFile, String distNo) {
        XxlJobLogger.log("generateOverseasWorkDetails start, distNo: {}", distNo);

        List<OverseasDistributionPaymentWorkDetail> details = distReportService.reportOverseasDistributionPaymentWorkDetail(distNo);

        // 定义标题和字段
        String[] title = new String[]{"Dist No", "Account Id", "Account Name", "Society Code", "overseas_dist_seq", "Work Code", "Work Title", "Royalties"};
        String[] key = new String[]{"distNo", "accountId", "accountName", "societyCode", "adjDistNo", "workCode", "workTitle", "royalties"};

        // 构造文件路径
        if (CommonUtils.isWindows()) {
            outParentFile = "E:\\Test";
        }
        String filePath = String.format("%s%s%s 6. Work Detail- Overseas Distribution Payment Work Detail.xlsx", outParentFile, File.separator, distNo);

        // 创建 Workbook
        Workbook workbook = new XSSFWorkbook();
        int sheetIndex = 0;
        int pageSize = 1_000_000; // 每页 100 万行
        int totalSize = details.size();
        int pageCount = (int) Math.ceil((double) totalSize / pageSize);

        try (FileOutputStream fileOutputStream = new FileOutputStream(filePath)) {
            for (int i = 0; i < pageCount; i++) {
                int fromIndex = i * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, totalSize);
                List<OverseasDistributionPaymentWorkDetail> subList = details.subList(fromIndex, toIndex);

                String sheetName = "Sheet" + (sheetIndex + 1);
                Sheet sheet = workbook.createSheet(sheetName);

                // 写入标题行
                Row headerRow = sheet.createRow(0);
                for (int j = 0; j < title.length; j++) {
                    Cell cell = headerRow.createCell(j);
                    cell.setCellValue(title[j]);
                }

                // 写入数据行
                int rowNum = 1;
                for (OverseasDistributionPaymentWorkDetail detail : subList) {
                    Row dataRow = sheet.createRow(rowNum++);
                    for (int j = 0; j < key.length; j++) {
                        Object value = ReflectionUtils.getFieldValue(detail, key[j]);
                        Cell cell = dataRow.createCell(j);
                        if (value instanceof String) {
                            cell.setCellValue((String) value);
                        } else if (value instanceof Integer) {
                            cell.setCellValue((Integer) value);
                        } else if (value instanceof BigDecimal) {
                            cell.setCellValue(((BigDecimal) value).doubleValue());
                        } else {
                            cell.setCellValue("");
                        }
                    }
                }

                sheetIndex++;
            }

            // 写入文件
            workbook.write(fileOutputStream);
            XxlJobLogger.log("generateOverseasWorkDetails end, distNo: {}", distNo);
        } catch (IOException e) {
            log.error("distNo:【{}】，文件生成失败！", distNo, e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("workbook.close() error", e);
            }
        }
    }*/
    @Autowired
    private DistAutoNetPayExportService distAutoNetPayExportService;

    public void generate730PDF(String outParentFile, String autopayNo) throws JRException, FileNotFoundException {

        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        XxlJobLogger.log("generate730PDF start, autopayNo: {}", autopayNo);
        String filePath = "/ireport/jrxml/distAutoPays/report730/730_main.jrxml";
        List<Report730MainVO> report730MainVOS = distAutoNetPayExportService.exportReport730(autopayNo, null, null, "", null);
        List<JasperParam> jaspers = new ArrayList<>();
        for (Report730MainVO report730MainVO : report730MainVOS) {
            Map<String,Object> param = new HashMap<>();
            // 主报表参数
            param.put("date", report730MainVO.getDate() != null ? report730MainVO.getDate() : "");
            param.put("autoPayNo", report730MainVO.getAutoPayNo() != null ? report730MainVO.getAutoPayNo() : "");
            param.put("memberName", report730MainVO.getMemberName() != null ? report730MainVO.getMemberName() : "");
            param.put("ipBaseNo", report730MainVO.getIpBaseNo() != null ? report730MainVO.getIpBaseNo() : "");
            param.put("paNameNo", report730MainVO.getPaNameNo() != null ? report730MainVO.getPaNameNo() : "");

            // 金额参数 - 格式化为字符串，避免类型转换错误
            param.put("totalRoyalties", formatBigDecimal(report730MainVO.getTotalRoyalties()));
            param.put("totalCommission", formatBigDecimal(report730MainVO.getTotalCommission()));
            param.put("totalTax", formatBigDecimal(report730MainVO.getTotalTax()));
            param.put("distributionPayment", formatBigDecimal(report730MainVO.getDistributionPayment()));
            param.put("netPayment", formatBigDecimal(report730MainVO.getNetPayment()));


            // 子报表数据源 - 这是关键部分
            param.put("report730SubDataSource", new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(
                    Collections.singletonList(report730MainVO.getReport730SubList())));
            param.put("report730SubTwoListDataSource", new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource(
                    report730MainVO.getReport730SubTwoList()));
            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");
            // 主报表数据源
            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(report730MainVO).getBytes()));
        }
        String outFile = String.format("%s/%s.pdf", outParentFile,"report730");
        try {
            PdfReport.exprtPdf(outFile, jaspers, filePath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    private String formatBigDecimal(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("#,##0.00");
        return df.format(value);
    }


}
