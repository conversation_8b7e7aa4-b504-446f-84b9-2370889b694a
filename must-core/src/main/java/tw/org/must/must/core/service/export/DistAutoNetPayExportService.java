package tw.org.must.must.core.service.export;

import tw.org.must.must.model.report.distAutoPay.*;
import tw.org.must.must.model.report.distAutoPay.report730VO.Report730MainVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DistAutoNetPayExportService {

    /**
     * DIVA780海外收據
     *
     * @param autopayNo
     * @param bankInfo
     * @param societyCode
     * @return
     */
    List<DistAutoPayOverseasReceipt> exportOverseasReceipt(String autopayNo, Boolean bankInfo, Integer societyCode, String distNo);

    /**
     * 780 - 支持日期过滤
     * @param autopayNo
     * @param bankInfo
     * @param societyCode
     * @param distNo
     * @param autopayDate 支付日期过滤条件
     * @return
     */
    List<DistAutoPayOverseasReceipt> exportOverseasReceipt(String autopayNo, Boolean bankInfo, Integer societyCode, String distNo, String autopayDate);

    /**
     * sales tax report（DIVA700）团体会员请款单
     *
     * @param paNameNoList
     * @return
     */
    List<SalesTaxReport> exportSaleTaxReport(List<String> paNameNoList);

    /**
     * 880 750
     *
     * @param autopayNo 支付编号
     * @return
     */
    List<DistPayList> exportPayList(String autopayNo);

    /**
     * 800协会 会员
     *
     * @param autopayNo type 1表示协会 2表示会员
     * @return
     */
    List<DistTranferList> exportTranferList(String autopayNo, Integer type);

    /**
     * 730支票 汇款
     *
     * @param autopayNo
     * @param list      支票（C） 汇款 （T、A）
     * @return
     */
    List<DistDistributionAccount> exportDistribution(String autopayNo, List<String> list, String paNameNo, String distNo);

    /**
     * 不分组
     * @param autopayNo
     * @param list
     * @param paNameNoList
     * @param distNo
     * @param payDate
     * @return
     */
    List<DistDistributionAccount> exportDistribution(String autopayNo, List<String> list, List<String> paNameNoList, String distNo, Date payDate);

    /**
     * 分组
     * @param autopayNo
     * @param list
     * @param paNameNoList
     * @param distNo
     * @param payDate
     * @return
     */
    Map<String,DistDistributionAccount> exportDistributionGroup(String autopayNo, List<String> list, List<String> paNameNoList, String distNo, Date payDate);

    /**
     *
     * @param autopayNo
     * @return
     */
    List<String> txtReport(String autopayNo);



    /**
     * 不分组
     * @param autopayNo
     * @param list
     * @param paNameNoList
     * @param distNo
     * @param payDate
     * @return
     */
    List<Report730MainVO> exportReport730(String autopayNo, List<String> list, List<String> paNameNoList, String distNo, Date payDate);

    /**
     * 分组
     * @param autopayNo
     * @param list
     * @param paNameNoList
     * @param distNo
     * @param payDate
     * @return
     */
    Map<String,Report730MainVO> exportReport730Group(String autopayNo, List<String> list, List<String> paNameNoList, String distNo, Date payDate);

}
