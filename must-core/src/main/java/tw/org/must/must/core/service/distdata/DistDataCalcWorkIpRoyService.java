package tw.org.must.must.core.service.distdata;


import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.report.DistWorkPointEnquiry;
import tw.org.must.must.model.report.DistributionNoUpa;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DistDataCalcWorkIpRoyService extends BaseService<DistDataCalcWorkIpRoy> {

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByCategoryCodeList(List<String> categoryCodeList, String distNo);

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByDistNo(String distNo);

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyByDistNoAndIp(String distNo,String ipBaseNo,String ipSocCode);

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByDistNoWithPage(String distNo,Integer pageNum, Integer pageSize);

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoySdListByDistNo(String distNo);

	Integer clear(String distNo);

	List<DistDataCalcWorkIpRoy> getByPointId(Long pointId);

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByDistNoAndWorkUniqueKeys(String distNo, List<String> workUniqueKeys);

	List<DistDataCalcWorkIpRoy> getByRetanId(Long retainId);

    List<DistDataCalcWorkIpRoy> getByDistNo(String distNo);

    List<DistWorkPointEnquiry> getDistWorkPointEnquiry(String distNo);

    List<DistributionNoUpa> getDistributionNoUpa(String distNo);

    Map<Long, String> getSourceTypeByRetainIds(List<Long> retainIds);

	Map<Long, BigDecimal> getCalcWorkIpRoyGroupByFid(String distNo) ;

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyNotMust(String distNo);

	List<DistDataCalcWorkIpRoy> getTotalNetPointGroupByCategoryCode(String distNo,List<String> categoryCodeList);

	List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByCategoryCodeListAndId(List<String> categoryCodeList, String distNo, Long id);

	List<DistDataCalcWorkIpRoy> getIpRoysByDistAndRetain(String distNo, Long retainId);

	List<String> getWorkUniqueKeysByRetain( Long retainId);

	List<DistDataCalcWorkIpRoy> getIpRoysByWorkUniquekey(String distNo, String workUniqueKey);

	List<DistDataCalcWorkIpRoy> test();

	/**
	 * 根据 retain_id 查询 work_unique_key、title_id 和 dist_roy 汇总
	 * @param retainId retain_id
	 * @return 包含 work_unique_key、title_id 和 total_dist_roy 的 Map 列表
	 */
	List<Map<String, Object>> getWorkUniqueKeyAndDistRoyByRetainId(Long retainId);

}