package tw.org.must.must.core.service.distdata.impl;

import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkIpRoyService;
import tw.org.must.must.mapper.distdata.DistDataCalcWorkIpRoyMapper;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.report.DistWorkPointEnquiry;
import tw.org.must.must.model.report.DistributionNoUpa;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DistDataCalcWorkIpRoyServiceImpl extends BaseServiceImpl<DistDataCalcWorkIpRoy>
		implements DistDataCalcWorkIpRoyService {

	private final DistDataCalcWorkIpRoyMapper distDataCalcWorkIpRoyMapper;

	@Autowired
	public DistDataCalcWorkIpRoyServiceImpl(DistDataCalcWorkIpRoyMapper distDataCalcWorkIpRoyMapper) {
		super(distDataCalcWorkIpRoyMapper);
		this.distDataCalcWorkIpRoyMapper = distDataCalcWorkIpRoyMapper;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByCategoryCodeList(List<String> categoryCodeList,String distNo) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		criteria.andIn("categoryCode", categoryCodeList);
//		criteria.andNotEqualTo("ipSocietyCode", 99);
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByCategoryCodeListAndId(List<String> categoryCodeList, String distNo, Long id) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andGreaterThan("id", id);
		criteria.andEqualTo("distNo", distNo);
		if(CollectionUtils.isNotEmpty(categoryCodeList)){
			criteria.andIn("categoryCode", categoryCodeList);
		}
		RowBounds rowBounds = new RowBounds(0, Constants.BATCH_SIZE_10000);
		example.orderBy("id");
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExampleAndRowBounds(example,rowBounds);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getIpRoysByDistAndRetain(String distNo, Long retainId) {
		return distDataCalcWorkIpRoyMapper.getIpRoysByDistAndRetain(distNo,retainId);
	}

	@Override
	public List<String> getWorkUniqueKeysByRetain(Long retainId) {
		return distDataCalcWorkIpRoyMapper.getWorkUniqueKeysByRetain(retainId);
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getIpRoysByWorkUniquekey(String distNo, String workUniqueKey) {
		return distDataCalcWorkIpRoyMapper.getIpRoysByWorkUniquekey(distNo,workUniqueKey);
	}

	@Override
	public List<DistDataCalcWorkIpRoy> test() {
		return distDataCalcWorkIpRoyMapper.test();
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByDistNo(String distNo) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		criteria.andEqualTo("sd", "N");
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyByDistNoAndIp(String distNo,String ipBaseNo, String ipSocCode) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		if(StringUtils.isNotEmpty(ipBaseNo)){
			criteria.andEqualTo("ipBaseNo",ipBaseNo);
		}
		if(StringUtils.isNotEmpty(ipSocCode)){
			criteria.andEqualTo("ipSocietyCode",ipSocCode);
		}
		criteria.andEqualTo("sd", "N");
//		criteria.andCondition("ip_society_code <> '099' AND ip_society_code <> '000'") ;
		criteria.andCondition("point_id is not NULL") ;
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByDistNoWithPage(String distNo,Integer pageNum, Integer pageSize) {
		PageHelper.startPage(pageNum,pageSize);

		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		criteria.andEqualTo("sd", "N");
		return distDataCalcWorkIpRoyMapper.selectByExample(example);
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoySdListByDistNo(String distNo) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		criteria.andEqualTo("sd", "Y");
		// criteria.andEqualTo("isDist", "Y");
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public Integer clear(String distNo) {
		if (StringUtils.isBlank(distNo)) {
			return -1;
		}
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		return distDataCalcWorkIpRoyMapper.deleteByExample(example);
    }

    @Override
    public List<DistDataCalcWorkIpRoy> getByPointId(Long pointId) {
        Example example = new Example(DistDataCalcWorkIpRoy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("pointId", pointId);
        List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
        return distDataCalcWorkIpRoyList;
    }

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyListByDistNoAndWorkUniqueKeys(String distNo, List<String> workUniqueKeys) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		criteria.andIn("workUniqueKey", workUniqueKeys);
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getByRetanId(Long retainId) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("retainId", retainId);
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getByDistNo(String distNo) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistWorkPointEnquiry> getDistWorkPointEnquiry(String distNo) {
		return distDataCalcWorkIpRoyMapper.getDistWorkPointEnquiry(distNo);
	}

	@Override
	public List<DistributionNoUpa> getDistributionNoUpa(String distNo) {
		return distDataCalcWorkIpRoyMapper.getDistributionNoUpa(distNo);
	}

    @Override
    public Map<Long, String> getSourceTypeByRetainIds(List<Long> retainIds) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("retainId", retainIds);
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList.stream().collect(Collectors.toMap(DistDataCalcWorkIpRoy::getRetainId, DistDataCalcWorkIpRoy::getSourceType, (a, b) -> a));
	}

	@Override
	public Map<Long, BigDecimal> getCalcWorkIpRoyGroupByFid(String distNo) {
		List<DistDataCalcWorkIpRoy> list = distDataCalcWorkIpRoyMapper.getCalcWorkIpRoyGroupByFid(distNo);
		Map<Long, BigDecimal> map = new HashMap<>();
		if(CollectionUtils.isNotEmpty(list)){
			list.forEach( l -> map.put(l.getFileBaseId(),l.getDistRoy()));
		}
		return map;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyNotMust(String distNo) {
		Example example = new Example(DistDataCalcWorkIpRoy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
//		criteria.andCondition("ip_society_code not in ('099','000','161') and point_id is not NULL") ;
		criteria.andCondition("point_id is not NULL") ;
		List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectByExample(example);
		return distDataCalcWorkIpRoyList;
	}

	@Override
	public List<DistDataCalcWorkIpRoy> getTotalNetPointGroupByCategoryCode(String distNo, List<String> categoryCodeList) {
		return distDataCalcWorkIpRoyMapper.getTotalNetPointGroupByCategoryCode(distNo,categoryCodeList);
	}

	@Override
	public List<Map<String, Object>> getWorkUniqueKeyAndDistRoyByRetainId(Long retainId) {
		return distDataCalcWorkIpRoyMapper.getWorkUniqueKeyAndDistRoyByRetainId(retainId);
	}


}