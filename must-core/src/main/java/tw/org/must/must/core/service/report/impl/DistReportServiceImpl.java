package tw.org.must.must.core.service.report.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.core.service.dist.DistParamOverseasService;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkIpRoyService;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkPointService;
import tw.org.must.must.core.service.listoverseas.*;
import tw.org.must.must.core.service.ref.RefSocietyService;
import tw.org.must.must.core.service.report.DistReportService;
import tw.org.must.must.model.dist.DistParamOverseas;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.listoverseas.*;
import tw.org.must.must.model.ref.RefSociety;
import tw.org.must.must.model.report.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DistReportServiceImpl implements DistReportService {

    @Autowired
    private DistDataCalcWorkPointService distDataCalcWorkPointService;
    @Autowired
    private DistDataCalcWorkIpRoyService distDataCalcWorkIpRoyService;
    @Autowired
    private ListOverseasFileWorkMappingService listOverseasFileWorkMappingService;
    @Autowired
    private ListOverseasFileBaseService listOverseasFileBaseService;
    @Autowired
    private RefSocietyService refSocietyService;
    @Autowired
    private ListOverseasReceiptService listOverseasReceiptService;
    @Autowired
    private ListOverseasReceiptDetailsService listOverseasReceiptDetailsService;
    @Autowired
    private ListMatchDataOverseasMappingService listMatchDataOverseasMappingService;

    @Autowired
    private DistParamOverseasService distParamOverseasService;

    @Override
    public List<DistRoyaltiesOverNumberByWork> reportDistRoyaltiesOverNumberByWork(String distNo, BigDecimal number) {
        if (StringUtils.isBlank(distNo) || null == number) {
            return new ArrayList<>();
        }
        return distDataCalcWorkPointService.getDistRoyltiesOverNumberByWork(distNo, number);

    }


    //  2020-11-19 huyong: 可以一条SQL搞定
    @Override
    public List<DistWorkPointEnquiry> reportDistWorkPointEnquiry(String distNo) {
        if (StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        return distDataCalcWorkIpRoyService.getDistWorkPointEnquiry(distNo);
    }

    //  2020-11-23 huyong: 可以一条SQL搞定
    @Override
    public List<DistributionNoUpa> reportDistributionNoUpa(String distNo) {
        if (StringUtils.isNotBlank(distNo)) {
            return new ArrayList<>();
        }
        return distDataCalcWorkIpRoyService.getDistributionNoUpa(distNo);
    }

    //  2020-11-23 huyong: 可以一条SQL搞定
    @Override
    public List<OverseasDistributionPaymentWorkDetail> reportOverseasDistributionPaymentWorkDetail(String distNo) {
        if (StringUtils.isBlank(distNo) || !distNo.startsWith("O")) {
            return new ArrayList<>();
        }

        List<OverseasDistributionPaymentWorkDetail> result = distDataCalcWorkPointService.getOverseasDistributionPaymentWorkDetail(distNo);
        result.addAll(distDataCalcWorkPointService.getOverseasDistributionPaymentWorkDetailSoc(distNo));
        return result;
    }

    // TODO: 2020-11-23 huyong:  一条SQL搞定，暂时没数据，待验证
    //  SELECT
    //	IFNULL( SUM( t1.amount ), 0 ) detailAmount,
    //	IFNULL( SUM( t2.total_amount ), 0 ) headerAmount,
    //	t4.*
    //FROM
    //	list_overseas_file_work_mapping t1
    //	LEFT JOIN list_overseas_file_base t2 ON t2.id = t1.file_base_id
    //	LEFT JOIN list_overseas_receipt_details t3 ON t3.id = t2.receipt_details_id
    //	LEFT JOIN list_overseas_receipt t4 on t4.id = t3.receipt_id
    //WHERE
    //	t3.receipt_id = #{receiptId};
    @Override
    public List<OverseasCheckingAmount> reportOverseasCheckingAmount(String distNo) {
        if(StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        List<ListOverseasReceipt> receiptList = listOverseasReceiptService.getListOverseasReceiptList(distNo, null, null, null, null, null,null,null);
        if(CollectionUtils.isEmpty(receiptList)) {
            return new ArrayList<>();
        }
        List<OverseasCheckingAmount> result = new ArrayList<>(receiptList.size());
        for (ListOverseasReceipt listOverseasReceipt : receiptList) {
            OverseasCheckingAmount ova = new OverseasCheckingAmount();
            ova.setDistNo(distNo);
            ova.setDistId(listOverseasReceipt.getId());
            ova.setDistAmount(listOverseasReceipt.getDistAmount());
            ova.setReceiptDate(listOverseasReceipt.getReceiptDate());
            ova.setFooterAmount(listOverseasFileBaseService.selectAmountByReceiptId(listOverseasReceipt.getId()));
            ova.setDetailAmount(listOverseasFileWorkMappingService.selectAmountByReceiptId(listOverseasReceipt.getId()));
            result.add(ova);
        }
        return result;
    }

    //  2020-11-24 huyong: 一条SQL搞定
    @Override
    public List<OverseasCheckingNoMatchWork> reportOverseasCheckingNoMatchWork(String distNo) {
        if(StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        return listOverseasFileWorkMappingService.getOverseasCheckingNoMatchWork(distNo);
    }

    /**
     *
     * Overseas Distribution Payment Information 
     * @param distNo
     * @return
     */
    @Override
    public List<OverseasDistributionPaymentInfomation> reportOverseasDistributionPaymentInfomation(String distNo) {
        if(StringUtils.isBlank(distNo) || !distNo.startsWith("O")) {
            return new ArrayList<>();
        }

        List<DistDataCalcWorkIpRoy> list = distDataCalcWorkIpRoyService.getByDistNo(distNo);
        Map<Long, List<DistDataCalcWorkIpRoy>> fileBaseIdMap = list.stream().collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getFileBaseId));
        Map<Integer, RefSociety> allSocietyMap = refSocietyService.getAllSocietyMap();
        List<ListOverseasFileBase> listOverseasFileBases = listOverseasFileBaseService.listByIds(new ArrayList<>(fileBaseIdMap.keySet()));
        Map<Long, List<ListOverseasFileBase>> receiptIdMap = listOverseasFileBases.stream().collect(Collectors.groupingBy(ListOverseasFileBase::getReceiptId));
        List<ListOverseasReceiptDetails> listOverseasReceiptDetails = listOverseasReceiptDetailsService.getListOverseasReceiptDetailsByReceiptIds(new ArrayList<>(receiptIdMap.keySet()));
        Map<Long, ListOverseasReceiptDetails> receiptDetailsMap = listOverseasReceiptDetails.stream().collect(Collectors.toMap(ListOverseasReceiptDetails::getId, Function.identity(), (a, b) -> a));
        List<OverseasDistributionPaymentInfomation> result = new ArrayList<>(receiptDetailsMap.size());
        for (Long receiptDetailId : receiptDetailsMap.keySet()) {
            OverseasDistributionPaymentInfomation infomation = new OverseasDistributionPaymentInfomation();
            infomation.setDistNo(distNo);
            ListOverseasReceiptDetails receiptDetails = receiptDetailsMap.get(receiptDetailId);
            infomation.setRefNo(receiptDetails.getReceiptId());
            infomation.setPeriod(receiptDetails.getSourceDistYear());
            infomation.setOverseasDistSeq("" + receiptDetails.getSourceSocietyCode() + receiptDetails.getDistOrderNumber());
            RefSociety refSociety = allSocietyMap.get(receiptDetails.getSourceSocietyCode());
            if(null != refSociety) {
                infomation.setSocietyName(refSociety.getSocietyName());
            }
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoys = new ArrayList<>();
            List<ListOverseasFileBase> fileBases = receiptIdMap.get(receiptDetails.getReceiptId());
            if(CollectionUtils.isNotEmpty(fileBases)) {
                for (ListOverseasFileBase fileBase : fileBases) {
                    distDataCalcWorkIpRoys.addAll(fileBaseIdMap.getOrDefault(fileBase.getId(), new ArrayList<>()));
                }

                infomation.setFieAmount(fileBases.stream().filter( x -> null != x.getFieAmount()).map(ListOverseasFileBase :: getFieAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

            }
            if(!distDataCalcWorkIpRoys.isEmpty()) {
                BigDecimal netRoy = distDataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getNetPoint).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                infomation.setNetRoy(netRoy.setScale(0, RoundingMode.HALF_UP));
                infomation.setNonMemberRoy(distDataCalcWorkIpRoys.stream().filter(x -> StringUtils.equals(x.getIpSocietyCode(), "99") && null != x.getDistRoy()).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                infomation.setRetainRoy(distDataCalcWorkIpRoys.stream().filter(x -> !StringUtils.equals(x.getIpSocietyCode(), "161") && null != x.getDistRoy()).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                infomation.setSdRoy(distDataCalcWorkIpRoys.stream().filter(x -> StringUtils.equals(x.getSd(), "Y") && null != x.getDistRoy()).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            }
            infomation.setMustMemberRoy(receiptDetails.getTotalStatementAmount()); // TODO 这个金额待验证
            result.add(infomation);
        }
        return result;
    }

    @Override
    public List<DistOverseasCheckingIpNotInWork> reportOverseasCheckingIpNotInWork(String distNo) {

        List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distNo);

        List<Long> receiptIds = distParamOverseasList.stream().map(di -> di.getReceiptId()).distinct().collect(Collectors.toList());

        List<ListOverseasReceipt> receiptList = listOverseasReceiptService.listByIds(receiptIds);
        if(CollectionUtils.isEmpty(receiptList)){
            return new ArrayList<>();
        }

//        List<Long> detailIds = distParamOverseasList.stream().map(di -> di.getReceiptDetailsId()).distinct().collect(Collectors.toList());

        List<ListOverseasReceiptDetails> detailList = listOverseasReceiptDetailsService.getListOverseasReceiptDetailsByReceiptIds(receiptIds);
        if(CollectionUtils.isEmpty(detailList)){
            return new ArrayList<>();
        }


        List<ListOverseasFileBase> baseList = listOverseasFileBaseService.getListOverseasFileBaseByReceiptIds(receiptIds);
        List<Long> fids = baseList.stream().map(b -> b.getId()).collect(Collectors.toList());

        return listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByFidsAndRejectCode(fids,"F02");
    }

}
