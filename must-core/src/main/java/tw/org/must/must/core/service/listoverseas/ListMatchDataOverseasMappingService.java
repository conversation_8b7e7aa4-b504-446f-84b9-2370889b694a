package tw.org.must.must.core.service.listoverseas;


import com.github.pagehelper.PageInfo;
import org.apache.ibatis.annotations.Param;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.model.list.vo.ListMatchDataOverseasMappingExport;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseas;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.report.DistOverseasCheckingIpNotInWork;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;

public interface ListMatchDataOverseasMappingService extends BaseService<ListMatchDataOverseasMapping> {

	Integer saveList(List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList);

	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingList(Long fileMappingId);
	List<Long> getIds(String dataUniqueKey);

//	Integer checkListMatchDataOverseasMapping(List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList);

	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByOverseasIds(List<Long> overseasIds);

	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByOverseasId(Long overseasId);

//	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByMappingId(Long mappingId);

	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByMappingIds(List<Long> fileMappingIds);

//	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByFileMappingIdListAndStatus(List<Long> ids, int status);

	List<WrkWorkIpShare> checkAndSetStatus(ListMatchDataOverseasMapping listMatchDataOverseasMapping);

	List<ListMatchDataOverseasMapping> listRemittedIp(Long fId,String dataUniqueKey);

	void checkIp(String workUniqueKey,List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList);

	List<ListMatchDataOverseasMapping> getUnCheckByDataUniqueKey(String dataUniqueKey);

	/***
	 * @Description:  判断当前over对应的ip是否都已经审核完
	 * @Param: [listMatchDataOverseas]
	 * @return: java.lang.Boolean  true：审核完   false: 未审核完
	 * @Author: hanDa
	 * @Date: 2020/12/4 17:50
	 */
	Boolean ipCheckAll(ListMatchDataOverseas listMatchDataOverseas);

	List<String> getDataUniqueKeysWithDataUniqueKeys(List<String> checkedDataUniqueKeyList);

	List<DistOverseasCheckingIpNotInWork> getListMatchDataOverseasMappingListByFidsAndRejectCode(List<Long> fids, String rejectCode);

	List<DistOverseasCheckingIpNotInWork> getListMatchDataOverseasMappingListByFidsAndNotInWork(List<Long> fids, Integer notInWork);

//	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingListByFileBaseIds(List<Long> fids, Long startId);

	void addImport(Map<String, String> u, List<String> titleList, Long fid,List<ListMatchDataOverseas> overseaList,List<ListMatchDataOverseasMapping> mappingList);

	List<ListMatchDataOverseasMappingExport> getListMatchDataOverseasMappingForExport(Long startId, Long batch, Long matchWorkId, Integer matchWorkSoc, String matchWorkTitle,
																					  String distributedIp, String distributedIpName, Integer matchIpSoc, String distNo,
																					  Long fId, Integer remitSoc, String remitIpName, String remitWorkTitle,
																					  String dataUniqueKey, Integer rejectCode,Integer status);

	PageInfo<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingManual(Page page, ListMatchDataOverseas listMatchDataOverseas);

	void initOrDestroyWrkWorkIpshareMap(int action);

	void deleteByFileMappingId(Long fileMappingId);

	void deleteByFileMappingIds(List<Long> fileMappingIds);

	void checkAndSetStatusAuto(ListMatchDataOverseasMapping listMatchDataOverseasMapping);

	void checkAndSetStatusAuto(WrkWork wrkWork, WrkWorkTitle workTitle, List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList);

	void checkAndSetStatusAuto(List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList);

	void checkIpManual(String workUniqueKey, List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList);

	Boolean ipCheckAllByDataUniqueKey(String dataUniqueKey);

	String importAuditResults(Long id,String ipNameNo, String workIpRole);

	List<String> importAuditResults(Map<String,List<String>> map);

	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingByDataUniqueKey(String dataUniqueKey);

	Integer getCountByFidAndDataUniqueKey(Long fileBaseId, String dataUniqueKey);

	Integer updateByFileMappingId(ListMatchDataOverseasMapping listMatchDataOverseasMapping);

	Integer insertOnDuplicateKeyUpdate(List<ListMatchDataOverseasMapping> list);

	Map<Long, BigDecimal> getAmountGroupByFid(@Param("fileBaseIds") List<Long> fileBaseIds);

	List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingByIdAndDistNo(Long startId, String distNo);


}