package tw.org.must.must.core.service.distdata;


import java.util.List;

import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.distdata.DistDataSnapshotWorkIpShare;

public interface DistDataSnapshotWorkIpShareService extends BaseService<DistDataSnapshotWorkIpShare> {

	Integer clearDistDataSnapshot(String distNo);

	List<DistDataSnapshotWorkIpShare> getDistDataSnapshotWorkIpShareByHeaderId(Long claimCcid, int page, int index);

    void insertDuplicateList(List<DistDataSnapshotWorkIpShare> distDataSnapshotWorkIpShareListAll);

	List<DistDataSnapshotWorkIpShare> getDistDataSnapshotWorkIpShare(String distNo, String workUniqueKey, String rightType);
}