package tw.org.must.must.core.service.mbr.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.model.mbr.MbrMemberBankAccDtl;
import tw.org.must.must.mapper.mbr.MbrMemberBankAccDtlMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.mbr.MbrMemberBankAccDtlService;

@Service
public class MbrMemberBankAccDtlServiceImpl extends BaseServiceImpl<MbrMemberBankAccDtl> implements MbrMemberBankAccDtlService {

	private final MbrMemberBankAccDtlMapper mbrMemberBankAccDtlMapper;

    @Autowired
    public MbrMemberBankAccDtlServiceImpl(MbrMemberBankAccDtlMapper mbrMemberBankAccDtlMapper) {
        super(mbrMemberBankAccDtlMapper);
        this.mbrMemberBankAccDtlMapper = mbrMemberBankAccDtlMapper;
    }

    @Override
    public void deleteByIpBaseNo(String ipBaseNo) {
        mbrMemberBankAccDtlMapper.deleteByIpBaseNo(ipBaseNo);
    }

    @Override
    public List<MbrMemberBankAccDtl> selectByIpBaseNo(String ipBaseNo) {
        MbrMemberBankAccDtl mbrMemberBankAccDtlCon = new MbrMemberBankAccDtl();
        mbrMemberBankAccDtlCon.setIpBaseNo(ipBaseNo);
        return mbrMemberBankAccDtlMapper.select(mbrMemberBankAccDtlCon);
    }

    @Override
    public Integer addOrUpdateList(List<MbrMemberBankAccDtl> mbrMemberBankAccDtlList) {
        return mbrMemberBankAccDtlMapper.addOrUpdateList(mbrMemberBankAccDtlList);
    }

    @Override
    public MbrMemberBankAccDtl selectBankAddressByIpBaseNo(String ipBaseNo) {
        if (ipBaseNo == null){
            return null;
        }
        Example example = new Example(MbrMemberBankAccDtl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ipBaseNo", ipBaseNo);
        List<MbrMemberBankAccDtl> mbrMemberBankAccDtls = mbrMemberBankAccDtlMapper.selectByExample(example);
        if (mbrMemberBankAccDtls != null && !mbrMemberBankAccDtls.isEmpty()){
            return mbrMemberBankAccDtls.get(0);
        }
        return null;
    }
}