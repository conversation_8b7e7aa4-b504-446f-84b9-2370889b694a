package tw.org.must.must.core.service.distdata.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.x.protobuf.MysqlxDatatypes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.enums.SysSocEnum;
import tw.org.must.must.core.service.dist.DistCalcRetainService;
import tw.org.must.must.core.service.dist.DistParamInfoService;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkIpRoyService;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkPointService;
import tw.org.must.must.core.service.distdata.DistDataSnapshotWorkIpShareService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasMappingService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.mbr.MbrMemberInfoService;
import tw.org.must.must.core.service.ref.RefGenreDtlService;
import tw.org.must.must.core.service.ref.RefSocietyService;
import tw.org.must.must.core.service.wrk.WrkIswcService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.core.service.wrk.WrkWorkTitleService;
import tw.org.must.must.mapper.distdata.DistDataCalcWorkPointMapper;
import tw.org.must.must.mapper.listoverseas.ListMatchDataOverseasMappingMapper;
import tw.org.must.must.model.dist.DistCalcRetain;
import tw.org.must.must.model.dist.DistParamInfo;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.distdata.DistDataCalcWorkPoint;
import tw.org.must.must.model.distdata.DistDataSnapshotWorkIpShare;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseas;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMatchWork;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.mbr.MbrMemberInfo;
import tw.org.must.must.model.ref.RefSociety;
import tw.org.must.must.model.report.*;
import tw.org.must.must.model.wrk.WrkIswc;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkTitle;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DistDataCalcWorkPointServiceImpl extends BaseServiceImpl<DistDataCalcWorkPoint> implements DistDataCalcWorkPointService {

    private static final Logger log = LoggerFactory.getLogger(DistDataCalcWorkPointServiceImpl.class);

    private final DistDataCalcWorkPointMapper distDataCalcWorkPointMapper;

    @Autowired
    private RefSocietyService refSocietyService;
    @Autowired
    private DistParamInfoService distParamInfoService;
    @Autowired
    private WrkWorkService workService;
    @Autowired
    private WrkWorkTitleService wrkWorkTitleService;
    @Autowired
    private DistDataCalcWorkIpRoyService distDataCalcWorkIpRoyService;
    @Autowired
    private MbrIpNameService mbrIpNameService;
    @Autowired
    private DistCalcRetainService distCalcRetainService;

    @Autowired
    private ListMatchDataOverseasMappingMapper listMatchDataOverseasMappingMapper;

    @Autowired
    private RefGenreDtlService refGenreDtlService;

    @Autowired
    private WrkIswcService wrkIswcService;

    @Autowired
    private DistDataSnapshotWorkIpShareService distDataSnapshotWorkIpShareService;

    private Map<String,WrkWork> wrkWorkMap;

    private Map<String,MbrIpName> mbrIpNameMap;

    private Map<String,WrkIswc> wrkIswcMap;

    private Map<String,WrkWorkTitle> otWorkTitleMap;

    private Map<Long,WrkWorkTitle> workTitleMap;

    private Map<String,String> genreDtlMap;

    @Autowired
    public DistDataCalcWorkPointServiceImpl(DistDataCalcWorkPointMapper distDataCalcWorkPointMapper) {
        super(distDataCalcWorkPointMapper);
        this.distDataCalcWorkPointMapper = distDataCalcWorkPointMapper;
    }

    @Override
    public Integer clear(String distNo) {
        if (StringUtils.isBlank(distNo)) {
            return -1;
        }
        Example example = new Example(DistDataCalcWorkPoint.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distNo", distNo);
        return distDataCalcWorkPointMapper.deleteByExample(example);
    }

    @Override
    public List<DistDataCalcWorkPoint> getByDistNo(String distNo) {
        if (StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        Example example = new Example(DistDataCalcWorkPoint.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distNo", distNo);
        return distDataCalcWorkPointMapper.selectByExample(example);
    }

    @Override
    public List<DistDataCalcWorkPoint> getSpecialContidion(String distNo, String categoryCode, Long startId, Date startDate, Date endDate) {
        if (StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        Example example = new Example(DistDataCalcWorkPoint.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distNo", distNo);
        if (StringUtils.isNotBlank(categoryCode)) {
            criteria.andEqualTo("categoryCode", categoryCode);
        }
        if (startId != null) {
            criteria.andGreaterThan("id", startId);
        }
        if (startDate != null) {
            criteria.andGreaterThanOrEqualTo("performTime", startDate);
        }
        if (endDate != null){
            criteria.andLessThanOrEqualTo("performTime", endDate);
        }

        example.orderBy("id").asc();

        return distDataCalcWorkPointMapper.selectByExampleAndRowBounds(example,new RowBounds(0, Constants.BATCH_SIZE_10000));
    }


    @Override
    public List<DistRoyaltiesOverNumberByWork> getDistRoyltiesOverNumberByWork(String distNo, BigDecimal number) {
        return distDataCalcWorkPointMapper.getDistRoyltiesOverNumberByWork(distNo, number);
    }

    @Override
    public List<OverseasDistributionPaymentWorkDetail> getOverseasDistributionPaymentWorkDetail(String distNo) {
        return distDataCalcWorkPointMapper.getOverseasDistributionPaymentWorkDetail(distNo);
    }

    @Override
    public List<OverseasDistributionPaymentWorkDetail> getOverseasDistributionPaymentWorkDetailSoc(String distNo) {
        return distDataCalcWorkPointMapper.getOverseasDistributionPaymentWorkDetailSoc(distNo);
    }

    /**
     *
     * 1. 查询所有roy数据
     * 2. 过滤保留海外会员没有SD的roy数据
     * 3. 统计要输出的soc
     * 4. soc下的所有workUniqueKey
     *
     * @param distNo
     * @return
     */
    @Override
    public List<DistReportPAndSocietyData> exportDistReportPAndSocietyData(String distNo){
        List<DistReportPAndSocietyData> result = new ArrayList<>();

        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);

        List<DistCalcRetain> distCalcRetains = distCalcRetainService.listWithDistAndType(distNo,"S");

        Map<Integer, RefSociety> societyMap = refSocietyService.getAllSocietyMap();

        for(DistCalcRetain distCalcRetain : distCalcRetains){
            String ipBaseNo = distCalcRetain.getIpBaseNo();
            log.info("导出协会：{}",ipBaseNo);
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getIpRoysByDistAndRetain(distNo,distCalcRetain.getId());
            if (distDataCalcWorkIpRoyList.isEmpty()) {
                log.info("当前会员{}没有需要导出的会员权利金明细数据， distNo = 【{}】！",ipBaseNo, distNo);
                return new ArrayList<>();
            }

            Integer societyCode = Integer.valueOf(ipBaseNo);
            DistReportPAndSocietyData pAndSocietyData = new DistReportPAndSocietyData();
            // 头部
            DistReportPAndSocietyData.Header header = new DistReportPAndSocietyData.Header();
            header.setSocietyCode(societyCode);
            RefSociety refSociety = societyMap.get(societyCode);
            if (null != refSociety) {
                String societyName = refSociety.getSocietyName();
                if(societyName.indexOf("/") > -1){
                    societyName = societyName.replace("/","");
                }
                header.setSocietyName(societyName);
            }
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            pAndSocietyData.setHeader(header);

//            Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyRoyMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey()));

            Map<String,List<DistDataCalcWorkIpRoy>> workTypeMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getWorkType));

            List<WorkData> workDataList = new ArrayList<>();
            BigDecimal socTotalAmount= BigDecimal.ZERO;
            BigDecimal tvTotal = BigDecimal.ZERO;
            BigDecimal radioTotal = BigDecimal.ZERO;
            BigDecimal concertTotal = BigDecimal.ZERO;
            BigDecimal karaokeTotal = BigDecimal.ZERO;
            BigDecimal airlineTotal = BigDecimal.ZERO;
            BigDecimal otherTotal = BigDecimal.ZERO;

            for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry :workTypeMap.entrySet()){
                String workType = entry.getKey();
                List<DistDataCalcWorkIpRoy> workTypeList = entry.getValue();
                Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyRoyMap ;
                if(workType.equals("AV")){
                    workUniqueKeyRoyMap = workTypeList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey() +"_" + d.getTitleId() + d.getAvWorkId() + d.getAvWorkSociety()));
                } else {
                    workUniqueKeyRoyMap = workTypeList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey() +"_" + d.getTitleId() + "-" + d.getWorkType()));
                }

                for (String key : workUniqueKeyRoyMap.keySet()) {

                    String workUniqueKey = key.split("_")[0];
                    List<DistDataCalcWorkIpRoy> workUniqueKeyAllIpRoyList = workUniqueKeyRoyMap.get(key) ;
                    workUniqueKeyAllIpRoyList.forEach(w->{if (w.getIpBaseNo() == null)w.setIpBaseNo("");});

                    BigDecimal workIpTotalRoy = workUniqueKeyAllIpRoyList.stream().filter(x-> distCalcRetain.getId().equals(x.getRetainId())).map(it ->it.getDistRoy()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if(workIpTotalRoy.compareTo(BigDecimal.ZERO) < 1){
                        continue;
                    }
                    DistDataCalcWorkIpRoy firstIpRoy = workUniqueKeyAllIpRoyList.get(0);
//
                    WrkWork wrkWork = wrkWorkMap.get(workUniqueKey);
                    if(wrkWork == null){
                        wrkWork = workService.getWrkWorkByWorkUniqueKey(workUniqueKey);
                        wrkWorkMap.put(workUniqueKey,wrkWork);

                        if(wrkWork == null){
                            continue;
                        }
                    }

                    WrkIswc wrkIswc = wrkIswcMap.get(workUniqueKey);
                    if(wrkIswc == null){
                        List<WrkIswc> iswcs = wrkIswcService.getWrkIswcByWrkId(wrkWork.getWorkId(),wrkWork.getWorkSocietyCode());
                        if(CollectionUtils.isNotEmpty(iswcs) && StringUtils.isNotBlank(iswcs.get(0).getIswc())){
                            wrkIswc = iswcs.get(0);
                        }
                    }

                    WorkData workData = new WorkData();
                    List<WorkIpRoy> wips = new ArrayList<>();
                    workData.setWorkNo(workUniqueKey);
               /* if (wrkIswc != null) {
                    String iswc = wrkIswc.getIswc();
                    workData.setWorkNo(iswc.substring(0, 1) + "-" + iswc.substring(1));
                } else {
                    workData.setWorkNo(String.format("%s-%s", SysSocEnum.getByCode(wrkWork.getWorkSocietyCode()), wrkWork.getWorkId()));
                }*/

                    Optional<DistDataCalcWorkIpRoy> optional = workUniqueKeyAllIpRoyList.stream().filter(i->StringUtils.equals(i.getSd(),"Y")).findFirst();
                    if(optional.isPresent()){
                        workData.setSd("Y");
                    }

                    String title;
                    Long titleId = firstIpRoy.getTitleId();
                    WrkWorkTitle wrkWorkTitle = workTitleMap.get(titleId); //匹配的标题
                    WrkWorkTitle otWrkWorkTitle = otWorkTitleMap.get(workUniqueKey);//主标题
                    if(otWrkWorkTitle == null){
                        otWrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(workUniqueKey,"OT");
                        otWorkTitleMap.put(workUniqueKey,otWrkWorkTitle);
                    }

                    if (null == wrkWorkTitle) {
                        wrkWorkTitle = wrkWorkTitleService.getById(titleId);
                        if(wrkWorkTitle != null){
                            workTitleMap.put(titleId,wrkWorkTitle);
                        } else {
                            wrkWorkTitle = otWorkTitleMap.get(workUniqueKey);
                        }
                    }

                    if (null != wrkWorkTitle) {
                        title = StringUtils.isNotBlank(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitle() : wrkWorkTitle.getTitleEn();
                        if(!title.equalsIgnoreCase(wrkWorkTitle.getTitleEn())){
                            title = title.concat("(").concat(wrkWorkTitle.getTitleEn()).concat(")");
                        }
                        workData.setWorkTitle(title);
                    }

                    String originalTitle = "";
                    if(firstIpRoy.getRefWorkId() != null && firstIpRoy.getRefWorkId() != 0){
                        String refWorkUniqueKey = Constants.getWorkUniqueKey(firstIpRoy.getRefWorkSociety(),firstIpRoy.getRefWorkId());
                        WrkWorkTitle refWorkTitle = otWorkTitleMap.get(refWorkUniqueKey);
                        if( null == refWorkTitle){
                            refWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(refWorkUniqueKey,"OT");
                        }

                        if (null != refWorkTitle) {
                            originalTitle = String.format("Original Title: %s(Work No.%s-%s)",refWorkTitle.getTitleEn() == null ? "" : refWorkTitle.getTitleEn(), SysSocEnum.getByCode(refWorkTitle.getWorkSocietyCode()), refWorkTitle.getWorkId());
                            otWorkTitleMap.put(refWorkUniqueKey,refWorkTitle);
                        }
                    } else if(wrkWorkTitle.getSubTitleId() != 0  && otWrkWorkTitle != null){//非主标题
                        originalTitle = String.format("Original Title: %s(Work No.%s-%s)",otWrkWorkTitle.getTitleEn() == null ? "" : otWrkWorkTitle.getTitleEn(), SysSocEnum.getByCode(otWrkWorkTitle.getWorkSocietyCode()), otWrkWorkTitle.getWorkId());
                    }

                    if(firstIpRoy.getAvWorkId() != null && firstIpRoy.getAvWorkId() != 0){
                        String avWorkUniqueKey = Constants.getWorkUniqueKey(firstIpRoy.getAvWorkSociety(),firstIpRoy.getAvWorkId());
                        WrkWorkTitle avWrkWorkTitle = otWorkTitleMap.get(avWorkUniqueKey);//主标题
                        if(avWrkWorkTitle == null){
                            avWrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(avWorkUniqueKey,"OT");
                        }

                        if(avWrkWorkTitle != null && genreDtlMap.get(avWrkWorkTitle.getGenreCode()) != null){
                            String description = genreDtlMap.get(avWrkWorkTitle.getGenreCode());
                            if(StringUtils.isNotBlank(originalTitle)){
                                originalTitle = originalTitle +"\n";
                            }
                            originalTitle = originalTitle + String.format("From %s Title: %s(Work No.%s-%s)",description,avWrkWorkTitle.getTitleEn() == null ? "" : avWrkWorkTitle.getTitleEn(), SysSocEnum.getByCode(avWrkWorkTitle.getWorkSocietyCode()), avWrkWorkTitle.getWorkId());
                        }
                    }

                    if(StringUtils.isNotBlank(originalTitle)){
                        workData.setOriginaltitle(originalTitle);
                    }

                    Map<String,List<DistDataCalcWorkIpRoy>> groupMap = workUniqueKeyAllIpRoyList.stream()
                            .collect(Collectors.groupingBy(l -> l.getIpNameNo() + l.getWorkIpRole() + l.getGroupIndicator() + l.getIpSocietyCode(), LinkedHashMap::new,Collectors.toList()));

                    for (String groupKey : groupMap.keySet()) {
                        List<DistDataCalcWorkIpRoy> dataCalcWorkIpRoys = groupMap.get(groupKey);

                        DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = dataCalcWorkIpRoys.get(0);
                        WorkIpRoy wip = new WorkIpRoy();
                        MbrIpName ipName = mbrIpNameMap.get(distDataCalcWorkIpRoy.getIpNameNo());
                        if (null == ipName) {
                            ipName = mbrIpNameService.getIpNameByIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
                            mbrIpNameMap.put(distDataCalcWorkIpRoy.getIpNameNo(), ipName);
                        }
                        setIpName(ipName, wip);

                        wip.setIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
//                    String share = workIpRoy.getIpShare() + "%";
                        BigDecimal ipshare = dataCalcWorkIpRoys.stream().filter(d-> d.getFileMappingId().equals(distDataCalcWorkIpRoy.getFileMappingId())).map(DistDataCalcWorkIpRoy::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add);
                        wip.setShare(ipshare + "%");
                        wip.setSociety(distDataCalcWorkIpRoy.getIpSocietyCode());
                        wip.setStatus(distDataCalcWorkIpRoy.getWorkIpRole());

                        BigDecimal distAmount =  dataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (distAmount == null) {
                            distAmount = distDataCalcWorkIpRoy.getDistRoy();
                        }

//                        BigDecimal distRoy = distDataCalcWorkIpRoy.getDistRoy() ;
                        if (distCalcRetain.getId().equals(distDataCalcWorkIpRoy.getRetainId()) && distAmount.compareTo(BigDecimal.ZERO) == 1) {
                            wip.setTv("$0");
                            wip.setRadio("$0");
                            wip.setKaraoke("$0");
                            wip.setAirline("$0");
                            wip.setOthers("$0");
                            wip.setConcert("$0");
                            wip.setTotal(bigDecimaltoString(distAmount));
                            socTotalAmount = socTotalAmount.add(distAmount);
                            switch (distDataCalcWorkIpRoy.getPoolCode()) {
                                case "T":
                                    wip.setTv(bigDecimaltoString(distAmount));
                                    tvTotal = tvTotal.add(distAmount);
                                    break;
                                case "R":
                                    wip.setRadio(bigDecimaltoString(distAmount));
                                    radioTotal = radioTotal.add(distAmount);
                                    break;
                                case "C":
                                    wip.setConcert(bigDecimaltoString(distAmount));
                                    concertTotal = concertTotal.add(distAmount);
                                    break;
                                case "K":
                                case "F":
                                    wip.setKaraoke(bigDecimaltoString(distAmount));
                                    karaokeTotal = karaokeTotal.add(distAmount);
                                    break;
                                case "G":
                                    wip.setAirline(bigDecimaltoString(distAmount));
                                    airlineTotal = airlineTotal.add(distAmount);
                                    break;
                                case "O":
                                case "I":
                                    wip.setOthers(bigDecimaltoString(distAmount));
                                    otherTotal = otherTotal.add(distAmount);
                                    break;
                            }
                        }

                        wip.setRoyMap(distCalcRetain.getRoyExtJson());
                        wips.add(wip);
                    }
                    workData.setWorkIpRoyList(wips);
                    workDataList.add(workData);
                }
            }

            pAndSocietyData.setWorkData(workDataList);

            // 汇总
            Summary summary = new Summary();
            summary.setTotalWorks(workDataList.size());
//            summary.setTotalFormat(bigDecimaltoString(socTotalAmount));
            summary.setTotal(bigDecimaltoString(socTotalAmount),bigDecimaltoString(tvTotal),bigDecimaltoString(radioTotal),
                    bigDecimaltoString(concertTotal),bigDecimaltoString(karaokeTotal),bigDecimaltoString(airlineTotal),bigDecimaltoString(otherTotal));


            Map<String, BigDecimal> royMap = new HashMap<>();
            String royExtJson = distCalcRetain.getRoyExtJson();
            JSONObject jsonObject = JSON.parseObject(royExtJson);
            for (String key : jsonObject.keySet()) {
                BigDecimal roy = (BigDecimal) jsonObject.get(key);
                BigDecimal oldRoy = royMap.getOrDefault(key, BigDecimal.ZERO);
                royMap.put(key, roy.add(oldRoy));
            }
            royMap.entrySet().forEach(en -> {
                en.getValue().setScale(0,RoundingMode.HALF_UP);
            });
            summary.setTotalRoyMap(royMap);

            pAndSocietyData.setSummary(summary);

            result.add(pAndSocietyData);

        }

        return result;
    }

    @Override
    public List<DistReportPAndSocietyData> exportDistReportOSocietyData(String distNo) {

        List<DistReportPAndSocietyData> result = new ArrayList<>();

        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);

        List<DistCalcRetain> distCalcRetains = distCalcRetainService.listWithDistAndType(distNo,"S");

        Map<Integer, RefSociety> societyMap = refSocietyService.getAllSocietyMap();

        for(DistCalcRetain distCalcRetain : distCalcRetains){
            String ipBaseNo = distCalcRetain.getIpBaseNo();
            log.info("导出协会：{}",ipBaseNo);
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getIpRoysByDistAndRetain(distNo,distCalcRetain.getId());
            if (distDataCalcWorkIpRoyList.isEmpty()) {
                log.info("当前会员{}没有需要导出的会员权利金明细数据， distNo = 【{}】！",ipBaseNo, distNo);
                return new ArrayList<>();
            }

            Integer societyCode = Integer.valueOf(ipBaseNo);
            DistReportPAndSocietyData pAndSocietyData = new DistReportPAndSocietyData();
            // 头部
            DistReportPAndSocietyData.Header header = new DistReportPAndSocietyData.Header();
            header.setSocietyCode(societyCode);
            RefSociety refSociety = societyMap.get(societyCode);
            if (null != refSociety) {
                String societyName = refSociety.getSocietyName();
                if(societyName.indexOf("/") > -1){
                    societyName = societyName.replace("/","");
                }
                header.setSocietyName(societyName);
            }
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            pAndSocietyData.setHeader(header);

//            Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyRoyMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey()));

            List<WorkData> workDataList = new ArrayList<>();
            BigDecimal socTotalAmount= BigDecimal.ZERO;
            BigDecimal tvTotal = BigDecimal.ZERO;
            BigDecimal radioTotal = BigDecimal.ZERO;
            BigDecimal concertTotal = BigDecimal.ZERO;
            BigDecimal karaokeTotal = BigDecimal.ZERO;
            BigDecimal airlineTotal = BigDecimal.ZERO;
            BigDecimal otherTotal = BigDecimal.ZERO;
            Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyRoyMap = distDataCalcWorkIpRoyList.stream().filter(d->StringUtils.isBlank(d.getWorkUniqueKey())).collect(Collectors.groupingBy(d->d.getWorkUniqueKey()));
            for(String workUniqueKey : workUniqueKeyRoyMap.keySet()){
                List<DistDataCalcWorkIpRoy> workUniqueKeyAllIpRoyList = workUniqueKeyRoyMap.get(workUniqueKey);

                List<Long> fileMappingIds = workUniqueKeyAllIpRoyList.stream().map(DistDataCalcWorkIpRoy::getFileMappingId).collect(Collectors.toList());

//                List<ListMatchDataOverseasMapping> listMatchDataOverseasMappings = listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByFileMappingIdListAndStatus(fileMappingIds);
            }

//            for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry :workTypeMap.entrySet()){
//                String workType = entry.getKey();
//                List<DistDataCalcWorkIpRoy> workTypeList = entry.getValue();
//                Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyRoyMap ;
//                if(workType.equals("AV")){
//                    workUniqueKeyRoyMap = workTypeList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey() +"_" + d.getTitleId() + d.getAvWorkId() + d.getAvWorkSociety()));
//                } else {
//                    workUniqueKeyRoyMap = workTypeList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey() +"_" + d.getTitleId() + "-" + d.getWorkType()));
//                }
//
//                for (String key : workUniqueKeyRoyMap.keySet()) {
//
//                    String workUniqueKey = key.split("_")[0];
//                    List<DistDataCalcWorkIpRoy> workUniqueKeyAllIpRoyList = workUniqueKeyRoyMap.get(key) ;
//                    workUniqueKeyAllIpRoyList.forEach(w->{if (w.getIpBaseNo() == null)w.setIpBaseNo("");});
//
//                    BigDecimal workIpTotalRoy = workUniqueKeyAllIpRoyList.stream().filter(x-> distCalcRetain.getId().equals(x.getRetainId())).map(it ->it.getDistRoy()).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    if(workIpTotalRoy.compareTo(BigDecimal.ZERO) < 1){
//                        continue;
//                    }
//                    DistDataCalcWorkIpRoy firstIpRoy = workUniqueKeyAllIpRoyList.get(0);
////
//                    WrkWork wrkWork = wrkWorkMap.get(workUniqueKey);
//                    if(wrkWork == null){
//                        wrkWork = workService.getWrkWorkByWorkUniqueKey(workUniqueKey);
//                        wrkWorkMap.put(workUniqueKey,wrkWork);
//
//                        if(wrkWork == null){
//                            continue;
//                        }
//                    }
//
//                    WrkIswc wrkIswc = wrkIswcMap.get(workUniqueKey);
//                    if(wrkIswc == null){
//                        List<WrkIswc> iswcs = wrkIswcService.getWrkIswcByWrkId(wrkWork.getWorkId(),wrkWork.getWorkSocietyCode());
//                        if(CollectionUtils.isNotEmpty(iswcs) && StringUtils.isNotBlank(iswcs.get(0).getIswc())){
//                            wrkIswc = iswcs.get(0);
//                        }
//                    }
//
//                    WorkData workData = new WorkData();
//                    List<WorkIpRoy> wips = new ArrayList<>();
//                    workData.setWorkNo(workUniqueKey);
//               /* if (wrkIswc != null) {
//                    String iswc = wrkIswc.getIswc();
//                    workData.setWorkNo(iswc.substring(0, 1) + "-" + iswc.substring(1));
//                } else {
//                    workData.setWorkNo(String.format("%s-%s", SysSocEnum.getByCode(wrkWork.getWorkSocietyCode()), wrkWork.getWorkId()));
//                }*/
//
//                    Optional<DistDataCalcWorkIpRoy> optional = workUniqueKeyAllIpRoyList.stream().filter(i->StringUtils.equals(i.getSd(),"Y")).findFirst();
//                    if(optional.isPresent()){
//                        workData.setSd("Y");
//                    }
//
//                    String title;
//                    Long titleId = firstIpRoy.getTitleId();
//                    WrkWorkTitle wrkWorkTitle = workTitleMap.get(titleId); //匹配的标题
//                    WrkWorkTitle otWrkWorkTitle = otWorkTitleMap.get(workUniqueKey);//主标题
//                    if(otWrkWorkTitle == null){
//                        otWrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(workUniqueKey,"OT");
//                        otWorkTitleMap.put(workUniqueKey,otWrkWorkTitle);
//                    }
//
//                    if (null == wrkWorkTitle) {
//                        wrkWorkTitle = wrkWorkTitleService.getById(titleId);
//                        if(wrkWorkTitle != null){
//                            workTitleMap.put(titleId,wrkWorkTitle);
//                        } else {
//                            wrkWorkTitle = otWorkTitleMap.get(workUniqueKey);
//                        }
//                    }
//
//                    if (null != wrkWorkTitle) {
//                        title = StringUtils.isNotBlank(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitle() : wrkWorkTitle.getTitleEn();
//                        if(!title.equalsIgnoreCase(wrkWorkTitle.getTitleEn())){
//                            title = title.concat("(").concat(wrkWorkTitle.getTitleEn()).concat(")");
//                        }
//                        workData.setWorkTitle(title);
//                    }
//
//                    String originalTitle = "";
//                    if(firstIpRoy.getRefWorkId() != null && firstIpRoy.getRefWorkId() != 0){
//                        String refWorkUniqueKey = Constants.getWorkUniqueKey(firstIpRoy.getRefWorkSociety(),firstIpRoy.getRefWorkId());
//                        WrkWorkTitle refWorkTitle = otWorkTitleMap.get(refWorkUniqueKey);
//                        if( null == refWorkTitle){
//                            refWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(refWorkUniqueKey,"OT");
//                        }
//
//                        if (null != refWorkTitle) {
//                            originalTitle = String.format("Original Title: %s(Work No.%s-%s)",refWorkTitle.getTitleEn() == null ? "" : refWorkTitle.getTitleEn(), SysSocEnum.getByCode(refWorkTitle.getWorkSocietyCode()), refWorkTitle.getWorkId());
//                            otWorkTitleMap.put(refWorkUniqueKey,refWorkTitle);
//                        }
//                    } else if(wrkWorkTitle.getSubTitleId() != 0  && otWrkWorkTitle != null){//非主标题
//                        originalTitle = String.format("Original Title: %s(Work No.%s-%s)",otWrkWorkTitle.getTitleEn() == null ? "" : otWrkWorkTitle.getTitleEn(), SysSocEnum.getByCode(otWrkWorkTitle.getWorkSocietyCode()), otWrkWorkTitle.getWorkId());
//                    }
//
//                    if(firstIpRoy.getAvWorkId() != null && firstIpRoy.getAvWorkId() != 0){
//                        String avWorkUniqueKey = Constants.getWorkUniqueKey(firstIpRoy.getAvWorkSociety(),firstIpRoy.getAvWorkId());
//                        WrkWorkTitle avWrkWorkTitle = otWorkTitleMap.get(avWorkUniqueKey);//主标题
//                        if(avWrkWorkTitle == null){
//                            avWrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(avWorkUniqueKey,"OT");
//                        }
//
//                        if(avWrkWorkTitle != null && genreDtlMap.get(avWrkWorkTitle.getGenreCode()) != null){
//                            String description = genreDtlMap.get(avWrkWorkTitle.getGenreCode());
//                            if(StringUtils.isNotBlank(originalTitle)){
//                                originalTitle = originalTitle +"\n";
//                            }
//                            originalTitle = originalTitle + String.format("From %s Title: %s(Work No.%s-%s)",description,avWrkWorkTitle.getTitleEn() == null ? "" : avWrkWorkTitle.getTitleEn(), SysSocEnum.getByCode(avWrkWorkTitle.getWorkSocietyCode()), avWrkWorkTitle.getWorkId());
//                        }
//                    }
//
//                    if(StringUtils.isNotBlank(originalTitle)){
//                        workData.setOriginaltitle(originalTitle);
//                    }
//
//                    Map<String,List<DistDataCalcWorkIpRoy>> groupMap = workUniqueKeyAllIpRoyList.stream()
//                            .collect(Collectors.groupingBy(l -> l.getIpNameNo() + l.getWorkIpRole() + l.getGroupIndicator() + l.getIpSocietyCode(), LinkedHashMap::new,Collectors.toList()));
//
//                    for (String groupKey : groupMap.keySet()) {
//                        List<DistDataCalcWorkIpRoy> dataCalcWorkIpRoys = groupMap.get(groupKey);
//
//                        DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = dataCalcWorkIpRoys.get(0);
//                        WorkIpRoy wip = new WorkIpRoy();
//                        MbrIpName ipName = mbrIpNameMap.get(distDataCalcWorkIpRoy.getIpNameNo());
//                        if (null == ipName) {
//                            ipName = mbrIpNameService.getIpNameByIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
//                            mbrIpNameMap.put(distDataCalcWorkIpRoy.getIpNameNo(), ipName);
//                        }
//                        setIpName(ipName, wip);
//
//                        wip.setIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
////                    String share = workIpRoy.getIpShare() + "%";
//                        BigDecimal ipshare = dataCalcWorkIpRoys.stream().filter(d-> d.getFileMappingId().equals(distDataCalcWorkIpRoy.getFileMappingId())).map(DistDataCalcWorkIpRoy::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        wip.setShare(ipshare + "%");
//                        wip.setSociety(distDataCalcWorkIpRoy.getIpSocietyCode());
//                        wip.setStatus(distDataCalcWorkIpRoy.getWorkIpRole());
//
//                        BigDecimal distAmount =  dataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        if (distAmount == null) {
//                            distAmount = distDataCalcWorkIpRoy.getDistRoy();
//                        }
//
////                        BigDecimal distRoy = distDataCalcWorkIpRoy.getDistRoy() ;
//                        if (distCalcRetain.getId().equals(distDataCalcWorkIpRoy.getRetainId()) && distAmount.compareTo(BigDecimal.ZERO) == 1) {
//                            wip.setTv("$0");
//                            wip.setRadio("$0");
//                            wip.setKaraoke("$0");
//                            wip.setAirline("$0");
//                            wip.setOthers("$0");
//                            wip.setConcert("$0");
//                            wip.setTotal(bigDecimaltoString(distAmount));
//                            socTotalAmount = socTotalAmount.add(distAmount);
//                            switch (distDataCalcWorkIpRoy.getPoolCode()) {
//                                case "T":
//                                    wip.setTv(bigDecimaltoString(distAmount));
//                                    tvTotal = tvTotal.add(distAmount);
//                                    break;
//                                case "R":
//                                    wip.setRadio(bigDecimaltoString(distAmount));
//                                    radioTotal = radioTotal.add(distAmount);
//                                    break;
//                                case "C":
//                                    wip.setConcert(bigDecimaltoString(distAmount));
//                                    concertTotal = concertTotal.add(distAmount);
//                                    break;
//                                case "K":
//                                case "F":
//                                    wip.setKaraoke(bigDecimaltoString(distAmount));
//                                    karaokeTotal = karaokeTotal.add(distAmount);
//                                    break;
//                                case "G":
//                                    wip.setAirline(bigDecimaltoString(distAmount));
//                                    airlineTotal = airlineTotal.add(distAmount);
//                                    break;
//                                case "O":
//                                case "I":
//                                    wip.setOthers(bigDecimaltoString(distAmount));
//                                    otherTotal = otherTotal.add(distAmount);
//                                    break;
//                            }
//                        }
//
//                        wip.setRoyMap(distCalcRetain.getRoyExtJson());
//                        wips.add(wip);
//                    }
//                    workData.setWorkIpRoyList(wips);
//                    workDataList.add(workData);
//                }
//            }

            pAndSocietyData.setWorkData(workDataList);

            // 汇总
            Summary summary = new Summary();
            summary.setTotalWorks(workDataList.size());
//            summary.setTotalFormat(bigDecimaltoString(socTotalAmount));
            summary.setTotal(bigDecimaltoString(socTotalAmount),bigDecimaltoString(tvTotal),bigDecimaltoString(radioTotal),
                    bigDecimaltoString(concertTotal),bigDecimaltoString(karaokeTotal),bigDecimaltoString(airlineTotal),bigDecimaltoString(otherTotal));


            Map<String, BigDecimal> royMap = new HashMap<>();
            String royExtJson = distCalcRetain.getRoyExtJson();
            JSONObject jsonObject = JSON.parseObject(royExtJson);
            for (String key : jsonObject.keySet()) {
                BigDecimal roy = (BigDecimal) jsonObject.get(key);
                BigDecimal oldRoy = royMap.getOrDefault(key, BigDecimal.ZERO);
                royMap.put(key, roy.add(oldRoy));
            }
            royMap.entrySet().forEach(en -> {
                en.getValue().setScale(0,RoundingMode.HALF_UP);
            });
            summary.setTotalRoyMap(royMap);

            pAndSocietyData.setSummary(summary);

            result.add(pAndSocietyData);

        }

        return result;
    }


    public List<DistReportPAndIPData> exportDistReportOverseasData(String distNo){

        List<DistCalcRetain> distCalcRetains = distCalcRetainService.listWithDistAndType(distNo,"N");
        List<DistCalcRetain> distCalcRetainLegals = distCalcRetainService.listWithDistAndType(distNo,"L");
        distCalcRetains.addAll(distCalcRetainLegals);
        //测试
        //List<DistCalcRetain> distCalcRetains = distCalcRetainService.listWithDistAndIpBaseNo(distNo, "I0018901272");

        List<DistReportPAndIPData> result = new ArrayList<>();

        // 获取分配参数信息
        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);

        // 循环遍历 distCalcRetains 列表
        for (DistCalcRetain distCalcRetain : distCalcRetains) {
            // 新建 DistReportPAndIPData 实体类对象
            DistReportPAndIPData data = new DistReportPAndIPData();

            // 设置头部信息
            DistReportPAndIPData.Header header = new DistReportPAndIPData.Header();
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            header.setIpBaseNo(distCalcRetain.getIpBaseNo());
            header.setPaNameNo(distCalcRetain.getPaNameNo());
            header.setMemberNo(distCalcRetain.getMemberNo());

            // 获取会员名称信息
            MbrIpName paMbrIpName = mbrIpNameService.getPaNameAndChiseseName(distCalcRetain.getIpBaseNo(), distCalcRetain.getPaNameNo());
            if (null != paMbrIpName) {
                String mustMemberName = StringUtils.isNotBlank(paMbrIpName.getChineseName()) ? paMbrIpName.getChineseName() : paMbrIpName.getName();
                if (!mustMemberName.equalsIgnoreCase(paMbrIpName.getName())) {
                    mustMemberName = mustMemberName.concat("(").concat(paMbrIpName.getName()).concat(")");
                }
                header.setMustMemberName(mustMemberName);
            }

            data.setHeader(header);

            // 根据 retain_id 查询 dist_data_calc_work_ip_roy 表获取 work_unique_key、title_id 和 total_dist_roy
            List<Map<String, Object>> workRoyList = distDataCalcWorkIpRoyService.getWorkUniqueKeyAndDistRoyByRetainId(distCalcRetain.getId());

            List<WorkData> workDataList = new ArrayList<>();
            BigDecimal totalAmount = BigDecimal.ZERO;

            // 遍历查询结果
            for (Map<String, Object> workRoyMap : workRoyList) {
                String workUniqueKey = (String) workRoyMap.get("work_unique_key");
                Long titleId = (Long) workRoyMap.get("title_id");
                BigDecimal totalDistRoy = (BigDecimal) workRoyMap.get("total_dist_roy");

                // 根据 work_unique_key 和 distNo 查询 dist_data_snapshot_work_ip_share 表
                List<DistDataSnapshotWorkIpShare> snapshotWorkIpShares = distDataSnapshotWorkIpShareService
                        .getDistDataSnapshotWorkIpShare(distNo, workUniqueKey, "PER");

                if (!snapshotWorkIpShares.isEmpty()) {
                    WorkData workData = new WorkData();
                    workData.setWorkNo(workUniqueKey);

                    // 根据 title_id 查询作品标题
                    if (titleId != null) {
                        WrkWorkTitle wrkWorkTitle = workTitleMap.get(titleId);
                        if (null == wrkWorkTitle) {
                            wrkWorkTitle = wrkWorkTitleService.getById(titleId);
                            if (wrkWorkTitle != null) {
                                workTitleMap.put(titleId, wrkWorkTitle);
                            }
                        }

                        if (null != wrkWorkTitle) {
                            String title = StringUtils.isNotBlank(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitle() : wrkWorkTitle.getTitleEn();
                            if (StringUtils.isNotBlank(wrkWorkTitle.getTitleEn()) && !title.equalsIgnoreCase(wrkWorkTitle.getTitleEn())) {
                                title = title.concat("(").concat(wrkWorkTitle.getTitleEn()).concat(")");
                            }
                            workData.setWorkTitle(title);
                        }
                    }

                    // 根据 snapshotWorkIpShares 设置 WorkData 属性
                    List<WorkIpRoy> workIpRoyList = new ArrayList<>();
                    for (DistDataSnapshotWorkIpShare snapshot : snapshotWorkIpShares) {
                        WorkIpRoy workIpRoy = new WorkIpRoy();
                        workIpRoy.setIpNameNo(snapshot.getIpNameNo());
                        workIpRoy.setSociety(snapshot.getIpSocietyCode());
                        workIpRoy.setStatus(snapshot.getWorkIpRole());
                        if (snapshot.getWorkIpShare() != null) {
                            workIpRoy.setShare(snapshot.getWorkIpShare() + "%");
                        }
                        workIpRoy.setTotal(bigDecimaltoString(totalDistRoy));
                        workIpRoy.setIpName(snapshot.getIpName());
                        // 设置 IP 名称
//                        if (StringUtils.isNotBlank(snapshot.getIpNameNo())) {
//                            MbrIpName ipName = mbrIpNameMap.get(snapshot.getIpNameNo());
//                            if (null == ipName) {
//                                ipName = mbrIpNameService.getIpNameByIpNameNo(snapshot.getIpNameNo());
//                                mbrIpNameMap.put(snapshot.getIpNameNo(), ipName);
//                            }
//                            setIpName(ipName, workIpRoy);
//                        }
                        workIpRoyList.add(workIpRoy);
                    }
                    workData.setWorkIpRoyList(workIpRoyList);
                    workData.setOthers(bigDecimaltoString(totalDistRoy));
                    workDataList.add(workData);
                    totalAmount = totalAmount.add(totalDistRoy);
                }
            }

            data.setWorkData(workDataList);

            // 设置汇总信息
            Summary summary = new Summary();
            summary.setTotalWorks(workDataList.size());
            // 格式化金额：正数显示为$123，负数显示为-$123，去掉小数位
            String formattedAmount;
            if (totalAmount.compareTo(BigDecimal.ZERO) >= 0) {
                // 正数或零：$123
                formattedAmount = "$" + totalAmount.setScale(0, RoundingMode.HALF_UP).toString();
            } else {
                // 负数：-$123
                formattedAmount = "-$" + totalAmount.abs().setScale(0, RoundingMode.HALF_UP).toString();
            }
            summary.setOthersTotal(formattedAmount);
            data.setSummary(summary);

            result.add(data);
        }

        return result;
    }

    @Override
    public List<DistReportPAndIPData> exportDistReportPAndIPData(String distNo){

        if(distNo.startsWith("O")){
            return exportDistReportOverseasData(distNo);
        }
        List<DistReportPAndIPData> result = new ArrayList<>();

        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);

        List<DistCalcRetain> distCalcRetains = distCalcRetainService.listWithDistAndType(distNo,"N");
        List<DistCalcRetain> distCalcRetainLegals = distCalcRetainService.listWithDistAndType(distNo,"L");
        distCalcRetains.addAll(distCalcRetainLegals);
        //测试用
        //List<DistCalcRetain> distCalcRetains = distCalcRetainService.listWithDistAndIpBaseNo(distNo, "I0018901272");


        for(DistCalcRetain distCalcRetain : distCalcRetains){
            String ipBaseNo = distCalcRetain.getIpBaseNo();

            log.info("导出会员：{}",ipBaseNo);
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getIpRoysByDistAndRetain(distNo,distCalcRetain.getId());
//            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.test();
            if (distDataCalcWorkIpRoyList.isEmpty()) {
                log.info("当前会员{}没有需要导出的会员权利金明细数据， distNo = 【{}】！",ipBaseNo, distNo);
                return new ArrayList<>();
            }


            DistReportPAndIPData distReportPAndIPData = new DistReportPAndIPData();
            // 头部
            DistReportPAndIPData.Header header = new DistReportPAndIPData.Header();
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            header.setIpBaseNo(ipBaseNo);
            header.setPaNameNo(distCalcRetain.getPaNameNo());
            MbrIpName paMbrIpName = mbrIpNameService.getPaNameAndChiseseName(ipBaseNo,distCalcRetain.getPaNameNo());
            if(null != paMbrIpName){
                String mustMemberName = StringUtils.isNotBlank(paMbrIpName.getChineseName()) ? paMbrIpName.getChineseName() : paMbrIpName.getName();
                if(!mustMemberName.equalsIgnoreCase(paMbrIpName.getName())){
                    mustMemberName = mustMemberName.concat("(").concat(paMbrIpName.getName()).concat(")");
                }

                header.setMustMemberName(mustMemberName);
            }
            header.setMemberNo(distCalcRetain.getMemberNo());
            distReportPAndIPData.setHeader(header);

            Map<String,List<DistDataCalcWorkIpRoy>> workTypeMap =
                    distDataCalcWorkIpRoyList.stream()
                            .collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getWorkType));

            List<WorkData> workDataList = new ArrayList<>();
            BigDecimal socTotalAmount= BigDecimal.ZERO;
            BigDecimal tvTotal = BigDecimal.ZERO;
            BigDecimal radioTotal = BigDecimal.ZERO;
            BigDecimal concertTotal = BigDecimal.ZERO;
            BigDecimal karaokeTotal = BigDecimal.ZERO;
            BigDecimal airlineTotal = BigDecimal.ZERO;
            BigDecimal otherTotal = BigDecimal.ZERO;

            for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry : workTypeMap.entrySet()){

                List<DistDataCalcWorkIpRoy> workTypeList = entry.getValue();
                String workType = entry.getKey();
                Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyRoyMap ;
                if(workType.equals("AV")){
                    workUniqueKeyRoyMap = workTypeList.stream()
                            .collect(Collectors.groupingBy(
                                    d->d.getWorkUniqueKey() +"_" + d.getTitleId() + d.getAvWorkId() + d.getAvWorkSociety()));
                } else {
                    workUniqueKeyRoyMap = workTypeList.stream().collect(Collectors.groupingBy(d->d.getWorkUniqueKey() +"_" + d.getTitleId()));
                }

                for (String key : workUniqueKeyRoyMap.keySet()) {

                    String workUniqueKey = key.split("_")[0];
                    List<DistDataCalcWorkIpRoy> workUniqueKeyAllIpRoyList = workUniqueKeyRoyMap.get(key) ;
                    workUniqueKeyAllIpRoyList.forEach(w->{if (w.getIpBaseNo() == null)w.setIpBaseNo("");});

                    BigDecimal workIpTotalRoy = workUniqueKeyAllIpRoyList.stream().filter(
                            x-> distCalcRetain.getId().equals(x.getRetainId())).map(it ->it.getDistRoy()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if(workIpTotalRoy.compareTo(BigDecimal.ZERO) < 1){
                        continue;
                    }
                    DistDataCalcWorkIpRoy firstIpRoy = workUniqueKeyAllIpRoyList.get(0);
//
                    WrkWork wrkWork = wrkWorkMap.get(workUniqueKey);
                    if(wrkWork == null){
                        wrkWork = workService.getWrkWorkByWorkUniqueKey(workUniqueKey);
                        wrkWorkMap.put(workUniqueKey,wrkWork);

                        if(wrkWork == null){
                            continue;
                        }
                    }

                    WrkIswc wrkIswc = wrkIswcMap.get(workUniqueKey);
                    if(wrkIswc == null){
                        List<WrkIswc> iswcs = wrkIswcService.getWrkIswcByWrkId(wrkWork.getWorkId(),wrkWork.getWorkSocietyCode());
                        if(CollectionUtils.isNotEmpty(iswcs) && StringUtils.isNotBlank(iswcs.get(0).getIswc())){
                            wrkIswc = iswcs.get(0);
                        }
                    }

                    WorkData workData = new WorkData();
                    List<WorkIpRoy> wips = new ArrayList<>();
                    workData.setWorkNo(workUniqueKey);
                /*if (wrkIswc != null) {
                    String iswc = wrkIswc.getIswc();
                    workData.setWorkNo(iswc.substring(0, 1) + "-" + iswc.substring(1));
                } else {
                    workData.setWorkNo(String.format("%s-%s", SysSocEnum.getByCode(wrkWork.getWorkSocietyCode()), wrkWork.getWorkId()));
                }*/

                    Optional<DistDataCalcWorkIpRoy> optionalSd = workUniqueKeyAllIpRoyList.stream().filter(i->StringUtils.equals(i.getSd(),"Y")).findFirst();
                    if(optionalSd.isPresent()){
                        workData.setSd("Y");
                    }

                    String title;
                    Long titleId = firstIpRoy.getTitleId();
                    WrkWorkTitle wrkWorkTitle = workTitleMap.get(titleId); //匹配的标题
                    WrkWorkTitle otWrkWorkTitle = otWorkTitleMap.get(workUniqueKey);//主标题
                    if(otWrkWorkTitle == null){
                        otWrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(workUniqueKey,"OT");
                        otWorkTitleMap.put(workUniqueKey,otWrkWorkTitle);
                    }

                    if (null == wrkWorkTitle) {
                        wrkWorkTitle = wrkWorkTitleService.getById(titleId);
                        if(wrkWorkTitle != null){
                            workTitleMap.put(titleId,wrkWorkTitle);
                        } else {
                            wrkWorkTitle = otWorkTitleMap.get(workUniqueKey);
                        }
                    }

                    if (null != wrkWorkTitle) {
                        title = StringUtils.isNotBlank(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitle() : wrkWorkTitle.getTitleEn();
                        if(!title.equalsIgnoreCase(wrkWorkTitle.getTitleEn())){
                            title = title.concat("(").concat(wrkWorkTitle.getTitleEn()).concat(")");
                        }
                        workData.setWorkTitle(title);
                    }

                    String originalTitle = "";
                    if(firstIpRoy.getRefWorkId() != null && firstIpRoy.getRefWorkId() != 0){
                        String refWorkUniqueKey = Constants.getWorkUniqueKey(firstIpRoy.getRefWorkSociety(),firstIpRoy.getRefWorkId());
                        WrkWorkTitle refWorkTitle = otWorkTitleMap.get(refWorkUniqueKey);
                        if( null == refWorkTitle){
                            refWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(refWorkUniqueKey,"OT");
                        }

                        if (null != refWorkTitle) {
                            originalTitle = String.format("Original Title: %s(Work No.%s-%s)",refWorkTitle.getTitleEn() == null ? "" : refWorkTitle.getTitleEn(), SysSocEnum.getByCode(refWorkTitle.getWorkSocietyCode()), refWorkTitle.getWorkId());
                            otWorkTitleMap.put(refWorkUniqueKey,refWorkTitle);
                        }
                    } else if(wrkWorkTitle.getSubTitleId() != 0  && otWrkWorkTitle != null){//非主标题
                        originalTitle = String.format("Original Title: %s(Work No.%s-%s)",otWrkWorkTitle.getTitleEn() == null ? "" : otWrkWorkTitle.getTitleEn(), SysSocEnum.getByCode(otWrkWorkTitle.getWorkSocietyCode()), otWrkWorkTitle.getWorkId());
                    }

                    if(firstIpRoy.getAvWorkId() != null && firstIpRoy.getAvWorkId() != 0){
                        String avWorkUniqueKey = Constants.getWorkUniqueKey(firstIpRoy.getAvWorkSociety(),firstIpRoy.getAvWorkId());
                        WrkWorkTitle avWrkWorkTitle = otWorkTitleMap.get(avWorkUniqueKey);//主标题
                        if(avWrkWorkTitle == null){
                            avWrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKeyAndType(avWorkUniqueKey,"OT");
                        }

                        if(avWrkWorkTitle != null && genreDtlMap.get(avWrkWorkTitle.getGenreCode()) != null){
                            String description = genreDtlMap.get(avWrkWorkTitle.getGenreCode());
                            if(StringUtils.isNotBlank(originalTitle)){
                                originalTitle = originalTitle +"\n";
                            }
                            originalTitle = originalTitle + String.format("From %s Title: %s(Work No.%s-%s)",description,avWrkWorkTitle.getTitleEn() == null ? "" : avWrkWorkTitle.getTitleEn(), SysSocEnum.getByCode(avWrkWorkTitle.getWorkSocietyCode()), avWrkWorkTitle.getWorkId());
                        }
                    }

                    if(StringUtils.isNotBlank(originalTitle)){
                        workData.setOriginaltitle(originalTitle);
                    }

                    Map<String,List<DistDataCalcWorkIpRoy>> groupMap = workUniqueKeyAllIpRoyList.stream()
                            .collect(Collectors.groupingBy(l -> l.getIpNameNo() + l.getWorkIpRole() + l.getGroupIndicator() + l.getIpSocietyCode(), LinkedHashMap::new,Collectors.toList()));
//                BigDecimal workIpTotalRoy = BigDecimal.ZERO; // 当前IP当前作品汇总金额，如果汇总金额为0  不需要输出
                    for (String groupKey : groupMap.keySet()) {
                        List<DistDataCalcWorkIpRoy> dataCalcWorkIpRoys = groupMap.get(groupKey);

                        DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = dataCalcWorkIpRoys.get(0);
                        WorkIpRoy wip = new WorkIpRoy();
                        MbrIpName ipName = mbrIpNameMap.get(distDataCalcWorkIpRoy.getIpNameNo());
                        if (null == ipName) {
                            ipName = mbrIpNameService.getIpNameByIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
                            mbrIpNameMap.put(distDataCalcWorkIpRoy.getIpNameNo(), ipName);
                        }
                        setIpName(ipName, wip);

                        wip.setIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
//                    String share = workIpRoy.getIpShare() + "%";
                        BigDecimal ipshare = dataCalcWorkIpRoys.stream().filter(d-> d.getFileMappingId().equals(distDataCalcWorkIpRoy.getFileMappingId())).map(DistDataCalcWorkIpRoy::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add);
                        wip.setShare(ipshare + "%");
                        wip.setSociety(distDataCalcWorkIpRoy.getIpSocietyCode());
                        wip.setStatus(distDataCalcWorkIpRoy.getWorkIpRole());

                        BigDecimal distAmount =  dataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        BigDecimal distRoy = distDataCalcWorkIpRoy.getDistRoy() ;
                        if (distCalcRetain.getId().equals(distDataCalcWorkIpRoy.getRetainId()) && distAmount.compareTo(BigDecimal.ZERO) == 1) {

                            wip.setTv("$0");
                            wip.setRadio("$0");
                            wip.setKaraoke("$0");
                            wip.setAirline("$0");
                            wip.setOthers("$0");
                            wip.setConcert("$0");
                            wip.setTotal(bigDecimaltoString(distAmount));
                            socTotalAmount = socTotalAmount.add(distAmount);
                            switch (distDataCalcWorkIpRoy.getPoolCode()) {
                                case "T":
                                    wip.setTv(bigDecimaltoString(distAmount));
                                    tvTotal = tvTotal.add(distAmount);
                                    break;
                                case "R":
                                    wip.setRadio(bigDecimaltoString(distAmount));
                                    radioTotal = radioTotal.add(distAmount);
                                    break;
                                case "C":
                                    wip.setConcert(bigDecimaltoString(distAmount));
                                    concertTotal = concertTotal.add(distAmount);
                                    break;
                                case "K":
                                case "F":
                                    wip.setKaraoke(bigDecimaltoString(distAmount));
                                    karaokeTotal = karaokeTotal.add(distAmount);
                                    break;
                                case "G":
                                    wip.setAirline(bigDecimaltoString(distAmount));
                                    airlineTotal = airlineTotal.add(distAmount);
                                    break;
                                case "O":
                                case "I":
                                    wip.setOthers(bigDecimaltoString(distAmount));
                                    otherTotal = otherTotal.add(distAmount);
                                    break;
                            }
                        }

                        wip.setRoyMap(distCalcRetain.getRoyExtJson());
                        wips.add(wip);
                    }
                    workData.setWorkIpRoyList(wips);
                    workDataList.add(workData);
                }
            }

            distReportPAndIPData.setWorkData(workDataList);

            // 汇总
            Summary summary = new Summary();
            summary.setTotalWorks(workDataList.size());
//            summary.setTotalFormat(bigDecimaltoString(socTotalAmount));
            summary.setTotal(
                    bigDecimaltoString(socTotalAmount),
                    bigDecimaltoString(tvTotal),
                    bigDecimaltoString(radioTotal),
                    bigDecimaltoString(concertTotal),
                    bigDecimaltoString(karaokeTotal),
                    bigDecimaltoString(airlineTotal),
//                    bigDecimaltoString(genralTotal),
                    bigDecimaltoString(otherTotal));

            Map<String, BigDecimal> royMap = new HashMap<>();
            String royExtJson = distCalcRetain.getRoyExtJson();
            JSONObject jsonObject = JSON.parseObject(royExtJson);
            for (String key : jsonObject.keySet()) {
                BigDecimal roy = (BigDecimal) jsonObject.get(key);
                BigDecimal oldRoy = royMap.getOrDefault(key, BigDecimal.ZERO);
                royMap.put(key, roy.add(oldRoy));
            }
            royMap.entrySet().forEach(en -> {
                en.getValue().setScale(0,RoundingMode.HALF_UP);
            });
            summary.setTotalRoyMap(royMap);

            distReportPAndIPData.setSummary(summary);

            result.add(distReportPAndIPData);

        }

        return result;
    }


    @Override
    public List<DistReportSDSRSocietyData> exportDistReportSDSRSocietyData(String distNo) {
        if (StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        List<DistDataCalcWorkPoint> distDataCalcWorkPointList = this.getByDistNo(distNo);
        distDataCalcWorkPointList = distDataCalcWorkPointList.stream().filter(x -> StringUtils.equals("Y", x.getIsDist()) && StringUtils.isNotBlank(x.getSourceDistNo())).collect(Collectors.toList());
        if (distDataCalcWorkPointList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<Integer, List<DistDataCalcWorkPoint>> listMap = distDataCalcWorkPointList.stream().collect(Collectors.groupingBy(DistDataCalcWorkPoint::getWorkSocietyCode));
        if (listMap.size() == 0) {
            return new ArrayList<>();
        }
        List<DistReportSDSRSocietyData> result = new ArrayList<>(listMap.size());
        // 相关关联字段数据准备
        Map<Integer, RefSociety> societyMap = refSocietyService.getSocietyMap(new ArrayList<>(listMap.keySet()));
        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);
        // 作品ipshare组成，需要全部的的数据（包括is_dist = N）
        List<DistDataCalcWorkIpRoy> dataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getByDistNo(distNo);
        Map<Long, List<DistDataCalcWorkIpRoy>> dataCalcWorkIpRoyByPointIdMap = dataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getPointId));
        Map<Long, DistCalcRetain> distCalcRetainMap = distCalcRetainService.listByIds(dataCalcWorkIpRoyList.stream().filter(x -> null != x.getRetainId()).map(DistDataCalcWorkIpRoy::getRetainId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(DistCalcRetain::getId, Function.identity(), (a, b) -> a));
        Map<String, MbrIpName> mbrIpNameMap = mbrIpNameService.getIpNameMapByIpNameNoList(dataCalcWorkIpRoyList.stream().filter(x -> StringUtils.isNotBlank(x.getPaNameNo())).map(DistDataCalcWorkIpRoy::getPaNameNo).distinct().collect(Collectors.toList()));

        for (Integer societyCode : listMap.keySet()) {
            DistReportSDSRSocietyData sdsrSocietyData = new DistReportSDSRSocietyData();
            // 头部
            DistReportSDSRSocietyData.Header header = new DistReportSDSRSocietyData.Header();
            header.setSocietyCode(societyCode);
            RefSociety refSociety = societyMap.get(societyCode);
            if (null != refSociety) {
                header.setSocietyName(refSociety.getSocietyName());
            }
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            sdsrSocietyData.setHeader(header);

            Map<String, List<WorkData>> oldDistWorkDataMap = new HashMap<>();
            // sdsr 会有个一个新distNo 对应多个oldDistNo
            Map<String, List<DistDataCalcWorkPoint>> adjDistMap = listMap.get(societyCode).stream().collect(Collectors.groupingBy(DistDataCalcWorkPoint::getSourceDistNo));
            for (String oldDistNo : adjDistMap.keySet()) {
                List<DistDataCalcWorkPoint> distDataCalcWorkPoints = adjDistMap.get(oldDistNo);
                List<WrkWork> wrkWorks = workService.getWrkWorkByWorkUniqueKeys(distDataCalcWorkPoints.stream().map(DistDataCalcWorkPoint::getWorkUniqueKey).distinct().collect(Collectors.toList()));
                Map<String, WrkWork> wrkWorkMap = wrkWorks.stream().collect(Collectors.toMap(WrkWork::getWorkUniqueKey, Function.identity(), (a, b) -> a));
                List<String> refWorkUniqueKey = wrkWorks.stream().filter(x -> null != x.getRefWorkId() && null != x.getRefWorkSociety()).map(y -> Constants.getWorkUniqueKey(y.getRefWorkSociety(), y.getRefWorkId())).distinct().collect(Collectors.toList());
                Map<Long, WrkWorkTitle> workTitleMap = wrkWorkTitleService.listByIds(distDataCalcWorkPoints.stream().filter(x -> null != x.getWorkTitleId()).map(DistDataCalcWorkPoint::getWorkTitleId).distinct().collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(WrkWorkTitle::getId, Function.identity(), (a, b) -> a));
                List<String> workUniqueKeys = distDataCalcWorkPoints.stream().filter(x -> null == x.getWorkTitleId()).map(DistDataCalcWorkPoint::getWorkUniqueKey).distinct().collect(Collectors.toList());
                workUniqueKeys.addAll(refWorkUniqueKey);
                Map<String, WrkWorkTitle> OtTitleMap = wrkWorkTitleService.getTitleMapByWorkUniqueKeyListMapDefaultOT(workUniqueKeys);

                Map<String, List<DistDataCalcWorkPoint>> map = distDataCalcWorkPoints.stream().collect(Collectors.groupingBy(DistDataCalcWorkPoint::getWorkUniqueKey));
                List<WorkData> workDataList = new ArrayList<>(map.size());
                for (String workUniqueKey : map.keySet()) {
                    String title;
                    List<DistDataCalcWorkPoint> workPoints = map.get(workUniqueKey);
                    DistDataCalcWorkPoint point = workPoints.get(0);

                    List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = dataCalcWorkIpRoyByPointIdMap.get(point.getId());
                    if (null == distDataCalcWorkIpRoyList) {
                        continue;
                    }
                    WorkData workData = new WorkData();
                    List<WorkIpRoy> wips = new ArrayList<>(distDataCalcWorkIpRoyList.size());
                    for (DistDataCalcWorkIpRoy workIpRoy : distDataCalcWorkIpRoyList) {
                        if (StringUtils.isBlank(workData.getWorkNo())) {
                            // 赋值一次就行了
                            WrkWork wrkWork = wrkWorkMap.get(workUniqueKey);
                            if (null != wrkWork && StringUtils.isNotBlank(wrkWork.getISWC())) {
                                workData.setWorkNo(wrkWork.getISWC());
                            } else {
                                workData.setWorkNo(String.format("%s/%s", point.getWorkId(), point.getWorkSocietyCode()));
                            }
                            WrkWorkTitle wrkWorkTitle = workTitleMap.get(point.getWorkTitleId());
                            if (null == wrkWorkTitle) {
                                wrkWorkTitle = OtTitleMap.get(workUniqueKey);
                            }
                            if (null != wrkWorkTitle) {
                                title = StringUtils.isNotBlank(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitle() : wrkWorkTitle.getTitleEn();
                                workData.setWorkTitle(title);
                            }
                            WrkWorkTitle refWorkTitle = workTitleMap.get(point.getRefWorkUniqueKey());
                            if (null != refWorkTitle) {
                                workData.setRefWorkTitle(StringUtils.isNotBlank(refWorkTitle.getTitle()) ? refWorkTitle.getTitle() : refWorkTitle.getTitleEn());
                            }
                        }
                        WorkIpRoy wip = new WorkIpRoy();
                        MbrIpName mbrIpName = mbrIpNameMap.get(workIpRoy.getPaNameNo());
                        setIpName(mbrIpName,wip);
                        wip.setIpNameNo(workIpRoy.getIpNameNo());
//                        String share = workIpRoy.getIpShare().setScale(6, RoundingMode.HALF_UP) + "%";
                        wip.setShare(workIpRoy.getIpShare() + "%");
                        wip.setSociety(workIpRoy.getIpSocietyCode());
                        wip.setStatus(workIpRoy.getWorkIpRole());

                        DistCalcRetain distCalcRetain = distCalcRetainMap.get(workIpRoy.getRetainId());
                        if (null != distCalcRetain) {
                            wip.setRoyMap(distCalcRetain.getRoyExtJson());
                            wip.setTotal(bigDecimaltoString(distCalcRetain.getSubTotalRoy()));
                            wip.setTv(bigDecimaltoString(distCalcRetain.getTvRoy()));
                            wip.setRadio(bigDecimaltoString(distCalcRetain.getRadioRoy()));
                            wip.setConcert(bigDecimaltoString(distCalcRetain.getConcertRoy()));
                            wip.setKaraoke(bigDecimaltoString(distCalcRetain.getKaraokeFilmRoy()));
                            wip.setAirline(bigDecimaltoString(distCalcRetain.getAirlineRoy()));
                            wip.setOthers(bigDecimaltoString(distCalcRetain.getOtherPerformanceRoy()));
                        }
                        wips.add(wip);
                    }
                    workData.setWorkIpRoyList(wips);
                    workDataList.add(workData);
                }
                oldDistWorkDataMap.put(oldDistNo, workDataList);
            }
            sdsrSocietyData.setOldDistWorkDataMap(oldDistWorkDataMap);

            // 汇总
            Summary summary = new Summary();
            summary.setTotalWorks(listMap.get(societyCode).size());
            List<DistDataCalcWorkIpRoy> dataCalcWorkIpRoys = dataCalcWorkIpRoyList.stream().filter(x -> x.getWorkSocietyCode().equals(societyCode)).collect(Collectors.toList());
            summary.setTotal(dataCalcWorkIpRoys.stream().filter(x -> null != x.getDistRoy()).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

            List<Long> ids = dataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getRetainId).distinct().collect(Collectors.toList());
            Map<String, BigDecimal> royMap = new HashMap<>();
            for (Long id : ids) {
                DistCalcRetain distCalcRetain = distCalcRetainMap.get(id);
                if (Objects.isNull(distCalcRetain)) {
                    continue;
                }
                String royExtJson = distCalcRetain.getRoyExtJson();
                JSONObject jsonObject = JSON.parseObject(royExtJson);
                for (String key : jsonObject.keySet()) {
                    BigDecimal roy = (BigDecimal) jsonObject.get(key);
                    BigDecimal oldRoy = royMap.getOrDefault(key, BigDecimal.ZERO);
                    royMap.put(key, roy.add(oldRoy));
                }
            }
            summary.setTotalRoyMap(royMap);
            sdsrSocietyData.setSummary(summary);
            result.add(sdsrSocietyData);
        }
        return result;
    }

    @Override
    public List<DistReportSDSRIPData> exportDistReportSDSRIPData(String distNo) {
        if (StringUtils.isBlank(distNo)) {
            return new ArrayList<>();
        }
        List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getByDistNo(distNo);


        List<DistDataCalcWorkIpRoy> mustAndIsDistWorkIpRoyList = distDataCalcWorkIpRoyList.stream().filter(x -> StringUtils.equals(x.getIpSocietyCode(), Constants.AGENCY) && StringUtils.equals(x.getIsDist(), Constants.STRING_DIST_YES)).collect(Collectors.toList());
        if (mustAndIsDistWorkIpRoyList.isEmpty()) {
            log.info("当前没有导出的must会员权利金明细数据， distNo = 【{}】！", distNo);
        }

        // 相关关联字段数据准备
        DistParamInfo distParamInfo = distParamInfoService.findParamInfoByDistNo(distNo);
        List<DistDataCalcWorkPoint> distDataCalcWorkPoints = this.listByIds(mustAndIsDistWorkIpRoyList.stream().filter(x -> null != x.getPointId()).map(DistDataCalcWorkIpRoy::getPointId).distinct().collect(Collectors.toList()));
        Map<Long, DistDataCalcWorkPoint> distDataCalcWorkPointMap = distDataCalcWorkPoints.stream().collect(Collectors.toMap(DistDataCalcWorkPoint::getId, Function.identity(), (a, b) -> a));


        List<WrkWork> wrkWorks = workService.getWrkWorkByWorkUniqueKeys(distDataCalcWorkPoints.stream().map(DistDataCalcWorkPoint::getWorkUniqueKey).distinct().collect(Collectors.toList()));
        Map<String, WrkWork> wrkWorkMap = wrkWorks.stream().collect(Collectors.toMap(WrkWork::getWorkUniqueKey, Function.identity(), (a, b) -> a));

        List<String> refWorkUniqueKey = wrkWorks.stream().filter(x -> null != x.getRefWorkId() && null != x.getRefWorkSociety()).map(y -> Constants.getWorkUniqueKey(y.getRefWorkSociety(), y.getRefWorkId())).distinct().collect(Collectors.toList());
        Map<Long, WrkWorkTitle> workTitleMap = wrkWorkTitleService.listByIds(distDataCalcWorkPoints.stream().filter(x -> null != x.getWorkTitleId()).map(DistDataCalcWorkPoint::getWorkTitleId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(WrkWorkTitle::getId, Function.identity(), (a, b) -> a));
        List<String> workUniqueKeyList = distDataCalcWorkPoints.stream().filter(x -> null == x.getWorkTitleId()).map(DistDataCalcWorkPoint::getWorkUniqueKey).distinct().collect(Collectors.toList());
        workUniqueKeyList.addAll(refWorkUniqueKey);
        Map<String, WrkWorkTitle> OtTitleMap = wrkWorkTitleService.getTitleMapByWorkUniqueKeyListMapDefaultOT(workUniqueKeyList);
        Map<Long, DistCalcRetain> distCalcRetainMap = distCalcRetainService.listByIds(mustAndIsDistWorkIpRoyList.stream().filter(x -> null != x.getRetainId()).map(DistDataCalcWorkIpRoy::getRetainId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(DistCalcRetain::getId, Function.identity(), (a, b) -> a));
        Map<String, MbrIpName> paNameMap = mbrIpNameService.getIpNameMapByIpNameNoList(mustAndIsDistWorkIpRoyList.stream().filter(x -> StringUtils.isNotBlank(x.getPaNameNo())).map(DistDataCalcWorkIpRoy::getPaNameNo).distinct().collect(Collectors.toList()));
        // ===============

        // 根据ipBaseNo 分类
        Map<String, List<DistDataCalcWorkIpRoy>> IpRoyMap = mustAndIsDistWorkIpRoyList.stream().collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getIpBaseNo));


        List<DistReportSDSRIPData> result = new ArrayList<>(IpRoyMap.size());
        for (String ipBaseNo : IpRoyMap.keySet()) {
            DistReportSDSRIPData distReportPAndIPData = new DistReportSDSRIPData();
            // 头部
            DistReportSDSRIPData.Header header = new DistReportSDSRIPData.Header();
            header.setDistributionNo(distNo);
            header.setDate(distParamInfo.getDistDate());
            header.setIpBaseNo(ipBaseNo);
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoys = IpRoyMap.get(ipBaseNo);
            DistDataCalcWorkIpRoy workIpRoy = distDataCalcWorkIpRoys.get(0);
            header.setPaNameNo(workIpRoy.getPaNameNo());
            MbrIpName mbrIpName = paNameMap.get(workIpRoy.getPaNameNo());
            if (null != mbrIpName) {
                String mustMemberName = StringUtils.isNotBlank(mbrIpName.getChineseName()) ? mbrIpName.getChineseName() : mbrIpName.getName();
                if(StringUtils.isNotEmpty(mbrIpName.getName())){
                    mustMemberName = mustMemberName.concat(mbrIpName.getName());
                }
                header.setMustMemberName(mustMemberName);
            }
            distReportPAndIPData.setHeader(header);
            // ====================

            // 该IP下的所有作品
            // 找到包含该IP的所有作品
            List<String> workUniqueKeys = distDataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getWorkUniqueKey).distinct().collect(Collectors.toList());
            List<WorkData> workDataList = new ArrayList<>(workUniqueKeys.size());
            for (String workUniqueKey : workUniqueKeys) {
                String title;
                Map<Long, List<DistDataCalcWorkIpRoy>> collect = distDataCalcWorkIpRoyList.stream().filter(x -> StringUtils.equals(workUniqueKey, x.getWorkUniqueKey())).collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getPointId));
                for (Long pointId : collect.keySet()) {
                    WorkData workData = new WorkData();
                    List<DistDataCalcWorkIpRoy> dataCalcWorkIpRoys = collect.get(pointId);
                    List<WorkIpRoy> wips = new ArrayList<>(dataCalcWorkIpRoys.size());
                    for (DistDataCalcWorkIpRoy distDataCalcWorkIpRoy : dataCalcWorkIpRoys) {
                        DistDataCalcWorkPoint point = distDataCalcWorkPointMap.get(pointId);
                        workData.setOldDistNo(point.getSourceDistNo());
                        WrkWork wrkWork = wrkWorkMap.get(workUniqueKey);
                        if (null != wrkWork && StringUtils.isNotBlank(wrkWork.getISWC())) {
                            workData.setWorkNo(wrkWork.getISWC());
                        } else {
                            workData.setWorkNo(String.format("%s/%s", point.getWorkId(), point.getWorkSocietyCode()));
                        }
                        WrkWorkTitle wrkWorkTitle = workTitleMap.get(point.getWorkTitleId());
                        if (null == wrkWorkTitle) {
                            wrkWorkTitle = OtTitleMap.get(workUniqueKey);
                        }
                        if (null != wrkWorkTitle) {
                            title = StringUtils.isNotBlank(wrkWorkTitle.getTitle()) ? wrkWorkTitle.getTitle() : wrkWorkTitle.getTitleEn();
                            workData.setWorkTitle(title);
                        }
                        WrkWorkTitle refWorkTitle = workTitleMap.get(point.getRefWorkUniqueKey());
                        if (null != refWorkTitle) {
                            workData.setRefWorkTitle(StringUtils.isNotBlank(refWorkTitle.getTitle()) ? refWorkTitle.getTitle() : refWorkTitle.getTitleEn());
                        }
                        WorkIpRoy wip = new WorkIpRoy();
                        MbrIpName ipName = paNameMap.get(distDataCalcWorkIpRoy.getPaNameNo());
                        setIpName(ipName,wip);

                        wip.setIpNameNo(distDataCalcWorkIpRoy.getIpNameNo());
//                        String share = distDataCalcWorkIpRoy.getIpShare().setScale(6, RoundingMode.HALF_UP) + "%";
                        wip.setShare(distDataCalcWorkIpRoy.getIpShare() + "%");
                        wip.setSociety(distDataCalcWorkIpRoy.getIpSocietyCode());
                        wip.setStatus(distDataCalcWorkIpRoy.getWorkIpRole());

                        DistCalcRetain distCalcRetain = distCalcRetainMap.get(distDataCalcWorkIpRoy.getRetainId());
                        if (null != distCalcRetain) {
                            wip.setRoyMap(distCalcRetain.getRoyExtJson());
                            wip.setTotal(bigDecimaltoString(distCalcRetain.getSubTotalRoy()));
                            wip.setTv(bigDecimaltoString(distCalcRetain.getTvRoy()));
                            wip.setRadio(bigDecimaltoString(distCalcRetain.getRadioRoy()));
                            wip.setConcert(bigDecimaltoString(distCalcRetain.getConcertRoy()));
                            wip.setKaraoke(bigDecimaltoString(distCalcRetain.getKaraokeFilmRoy()));
                            wip.setAirline(bigDecimaltoString(distCalcRetain.getAirlineRoy()));
                            wip.setOthers(bigDecimaltoString(distCalcRetain.getOtherPerformanceRoy()));
                        }
                        wips.add(wip);
                    }
                    workData.setWorkIpRoyList(wips);
                    workDataList.add(workData);
                    break; // 其他都是相同的作品
                }

            }
            distReportPAndIPData.setData(workDataList.stream().collect(Collectors.groupingBy(WorkData::getOldDistNo)));
            // =========== 根据ipBaseNo 分类

            // 汇总
            Summary summary = new Summary();
            summary.setTotalWorks(workDataList.size());
            summary.setTotal(distDataCalcWorkIpRoys.stream().filter(x -> null != x.getDistRoy()).map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            List<Long> ids = distDataCalcWorkIpRoys.stream().map(DistDataCalcWorkIpRoy::getRetainId).distinct().collect(Collectors.toList());
            Map<String, BigDecimal> royMap = new HashMap<>();
            for (Long id : ids) {
                DistCalcRetain distCalcRetain = distCalcRetainMap.get(id);
                if (Objects.isNull(distCalcRetain)) {
                    continue;
                }
                String royExtJson = distCalcRetain.getRoyExtJson();
                JSONObject jsonObject = JSON.parseObject(royExtJson);
                for (String key : jsonObject.keySet()) {
                    BigDecimal roy = (BigDecimal) jsonObject.get(key);
                    BigDecimal oldRoy = royMap.getOrDefault(key, BigDecimal.ZERO);
                    royMap.put(key, roy.add(oldRoy));
                }
            }
            summary.setTotalRoyMap(royMap);
            distReportPAndIPData.setSummary(summary);
            // ============== 汇总

            result.add(distReportPAndIPData);
        }
        return result;
    }

    @Override
    public List<String> getFileBaseIdList(String distNo) {
        return distDataCalcWorkPointMapper.selectFileBaseIdList(distNo);
    }

    @Override
    public void init() {
        wrkWorkMap = new HashMap<>();
        mbrIpNameMap = new HashMap<>();
        wrkIswcMap = new HashMap<>();
        workTitleMap = new HashMap<>();
        otWorkTitleMap = new HashMap<>();
        genreDtlMap = refGenreDtlService.getRefGenreDtlByWorkType("AV");
    }

    @Override
    public void clear() {
        wrkWorkMap.clear();
        mbrIpNameMap.clear();
        wrkIswcMap.clear();
        workTitleMap.clear();
        otWorkTitleMap.clear();
    }

    public String bigDecimaltoString(BigDecimal bigDecimal){
        if(bigDecimal == null){
            return null;
        }

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        try{
            String formattedNumber = numberFormat.format(bigDecimal);
            return "$" + formattedNumber;
        }catch (Exception e){
            e.printStackTrace();
        }

        return "$0" ;
    }

    public void setIpName(MbrIpName mbrIpName, WorkIpRoy workIpRoy){
        if (null != mbrIpName) {
            String ipName = StringUtils.isNotBlank(mbrIpName.getChineseName()) ? mbrIpName.getChineseName() : mbrIpName.getName();
            if(StringUtils.isNotEmpty(mbrIpName.getName()) && !ipName.equalsIgnoreCase(mbrIpName.getName())){
                ipName = ipName.concat("(").concat(mbrIpName.getName()).concat(")");
            }

            workIpRoy.setIpName(ipName);
        }
    }
}
