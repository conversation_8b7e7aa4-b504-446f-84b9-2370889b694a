package tw.org.must.must.core.service.report;

import tw.org.must.must.model.report.*;

import java.math.BigDecimal;
import java.util.List;

public interface DistReportService {


    /**
     * 查出該分配權利金超過五千(number)的作品
     * @param distNo
     * @param number
     * @return
     */
    List<DistRoyaltiesOverNumberByWork> reportDistRoyaltiesOverNumberByWork(String distNo, BigDecimal number);


    /**
     * 用途:該分配代號下每個category比對到的歌曲，依照usage type ,使用日期,次數，用T1T2表計算出作品的gross point
     * @param distNo
     * @return
     */
    List<DistWorkPointEnquiry> reportDistWorkPointEnquiry(String distNo);


    /**
     * 用途:找出該分配下不能分配到UPA金額的人。
     * @param distNo
     * @return
     */
    List<DistributionNoUpa> reportDistributionNoUpa(String distNo);

    /**
     * O分配來源協回及相關作品金額
     * @param distNo
     * @return
     */
    List<OverseasDistributionPaymentWorkDetail> reportOverseasDistributionPaymentWorkDetail(String distNo);


    /**
     * 檢核KEY IN 的水單金額Royalties Statement 是否跟收到的電子交換檔file header, detail吻合 ，是否有水單卻沒有電子交換檔或是有電子交換檔沒有水單。
     * @param distNo 原始清单的分配编号
     * @return
     */
    List<OverseasCheckingAmount> reportOverseasCheckingAmount(String distNo);


    /**
     * 檢核有沒有 沒有比到的 worknum。
     * @param distNo 原始清单的分配编号
     * @return
     */
    List<OverseasCheckingNoMatchWork> reportOverseasCheckingNoMatchWork(String distNo);

    /**
     * 檔案名稱: Overseas Distribution Payment information.xlsx
     * 用途:經過前面的enquiry,計算出每個協會能分多少,不能分的有多少
     * @param distNo
     * @return
     */
    List<OverseasDistributionPaymentInfomation> reportOverseasDistributionPaymentInfomation(String distNo);

    List<DistOverseasCheckingIpNotInWork> reportOverseasCheckingIpNotInWork(String distNo);

}
