package tw.org.must.must.core.service.distdata.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.distdata.DistDataSnapshotWorkIpShareService;
import tw.org.must.must.mapper.distdata.DistDataSnapshotWorkIpShareMapper;
import tw.org.must.must.model.distdata.DistDataSnapshotWorkIpShare;

@Service
public class DistDataSnapshotWorkIpShareServiceImpl extends BaseServiceImpl<DistDataSnapshotWorkIpShare> implements DistDataSnapshotWorkIpShareService {

	private final DistDataSnapshotWorkIpShareMapper distDataSnapshotWorkIpShareMapper;

    @Autowired
    public DistDataSnapshotWorkIpShareServiceImpl(DistDataSnapshotWorkIpShareMapper distDataSnapshotWorkIpShareMapper) {
        super(distDataSnapshotWorkIpShareMapper);
        this.distDataSnapshotWorkIpShareMapper = distDataSnapshotWorkIpShareMapper;
    }
	@Override
	public Integer clearDistDataSnapshot(String distNo) {
		if(StringUtils.isBlank(distNo)){
			return -1;
		}
		Example example = new Example(DistDataSnapshotWorkIpShare.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		return distDataSnapshotWorkIpShareMapper.deleteByExample(example);
	}
	@Override
	public List<DistDataSnapshotWorkIpShare> getDistDataSnapshotWorkIpShareByHeaderId(Long claimCcid, int page, int index) {
		if(null == claimCcid){
			return new ArrayList<>();
		}
		PageHelper.startPage(page,index);
		Example example = new Example(DistDataSnapshotWorkIpShare.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("ccidClaimHeaderId", claimCcid);
		return distDataSnapshotWorkIpShareMapper.selectByExample(example);
	}

	// TODO
	@Override
	public void insertDuplicateList(List<DistDataSnapshotWorkIpShare> distDataSnapshotWorkIpShareListAll) {
		distDataSnapshotWorkIpShareMapper.insertDuplicateList(distDataSnapshotWorkIpShareListAll);
	}

	@Override
	public List<DistDataSnapshotWorkIpShare> getDistDataSnapshotWorkIpShare(String distNo, String workUniqueKey, String rightType) {
		Example example = new Example(DistDataSnapshotWorkIpShare.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		criteria.andEqualTo("workUniqueKey", workUniqueKey);
		criteria.andEqualTo("rightType", rightType);
		return distDataSnapshotWorkIpShareMapper.selectByExample(example);
	}
}