package tw.org.must.must.mapper.listoverseas;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.vo.ListMatchDataOverseasMappingExport;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;
import tw.org.must.must.model.report.DistOverseasCheckingIpNotInWork;

@Repository
public interface ListMatchDataOverseasMappingMapper extends BaseMapper<ListMatchDataOverseasMapping> {
  List<Long> getIds(@Param("dataUniqueKey") String dataUniqueKey);
  Integer selectCountByIpNameNoAndUniqueK(@Param("ipNameNo") String ipNameNo,@Param("dataUniqueKey") String dataUniqueKey);

  List<DistOverseasCheckingIpNotInWork> getListMatchDataOverseasMappingListByFidsAndRejectCode(@Param("list") List<Long> list, @Param("rejectCode") String rejectCode);

  List<DistOverseasCheckingIpNotInWork> getListMatchDataOverseasMappingListByFidsAndNotInWork(@Param("list") List<Long> list, @Param("notInWork") Integer notInWork);

//  Integer getCountByFidAndDataUniqueKey(@Param("fileBaseId") Long fileBaseId,@Param("dataUniqueKey") String dataUniqueKey);

  Integer updateByFileMappingId(ListMatchDataOverseasMapping listMatchDataOverseasMapping);

  List<ListMatchDataOverseasMappingExport> getListMatchDataOverseasMappingForExport(@Param("matchWorkId") Long matchWorkId, @Param("matchWorkSoc") Integer matchWorkSoc,
                                                                                    @Param("matchWorkTitle") String matchWorkTitle, @Param("distributedIp") String distributedIp,
                                                                                    @Param("distributedIpName") String distributedIpName, @Param("matchIpSoc") Integer matchIpSoc,
                                                                                    @Param("remitSoc") Integer remitSoc, @Param("remitIpName") String remitIpName,@Param("start") Long start,
                                                                                    @Param("remitWorkTitle") String remitWorkTitle, @Param("dataUniqueKey") String dataUniqueKey,
                                                                                    @Param("batch") Long batch, @Param("fId") Long fId, @Param("rejectCode") Integer rejectCode,
                                                                                    @Param("status") Integer status,@Param("distNo") String distNo);


  Integer insertOnDuplicateKeyUpdate(List<ListMatchDataOverseasMapping> list);

  List<ListMatchDataOverseasMapping> getListMatchDataOverseasMappingGroupByFid(@Param("fileBaseIds") List<Long> list_match_data_overseas_mapping) ;
}