package tw.org.must.must.mapper.distdata;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.report.DistWorkPointEnquiry;
import tw.org.must.must.model.report.DistributionNoUpa;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface DistDataCalcWorkIpRoyMapper extends BaseMapper<DistDataCalcWorkIpRoy> {

    /**
     * @param distNo
     * @return
     */
    List<Integer> getIpSocietyCodeByDistNo(@Param("distNo") String distNo);

    List<String> getIpBaseNoByDistNo(@Param("distNo") String distNo);

    /**
     * @param distNo
     * @param workUniqueKeyList
     * @return
     */
    List<DistDataCalcWorkIpRoy> selectIpNameByDistNoAndIpSocietyCode(@Param("distNo") String distNo, @Param("workUniqueKeyList") List<String> workUniqueKeyList);

    /**
     * @param distNo
     * @param ipSocietyCode
     * @return
     */
    List<String> getWorkUniqueKeyByDisNoAndIpSociety(@Param("distNo") String distNo, @Param("ipSocietyCode") Integer ipSocietyCode);

    List<String> getWorkUniqueKeyByDisNoAndIpBaseNo(@Param("distNo") String distNo, @Param("ipBaseNo") String ipBaseNo);

    /**
     * @param distNo
     * @param workUniqueKey
     * @return
     */
    List<DistDataCalcWorkIpRoy> getDistDataCalcWorkIpRoyForWorkAndIPIList(@Param("distNo") String distNo, @Param("workUniqueKey") String workUniqueKey);

    /**
     * @param distNo
     * @param workUniqueKey
     * @return
     */
    List<DistDataCalcWorkIpRoy> getIpWithWorkList(@Param("distNo") String distNo, @Param("workUniqueKey") String workUniqueKey);

    List<DistWorkPointEnquiry> getDistWorkPointEnquiry(@Param("distNo") String distNo);

    List<DistributionNoUpa> getDistributionNoUpa(@Param("distNo") String distNo);

    @Select("select file_base_id,sum(ifnull(dist_roy,0)) as dist_roy from dist_data_calc_work_ip_roy where dist_no = #{distNo} group by file_base_id")
    List<DistDataCalcWorkIpRoy> getCalcWorkIpRoyGroupByFid(@Param("distNo") String distNo) ;

    List<DistDataCalcWorkIpRoy> getTotalNetPointGroupByCategoryCode(@Param("distNo") String distNo,@Param("categoryCodeList") List<String> categoryCodeList);

    Long getIsHasRoyByMember(@Param("distNo") String distNo,@Param("categoryCodeList") List<String> categoryCodeList,@Param("ipBaseNo")String ipBaseNo) ;

    Long getIsHasRoyBySoc(@Param("distNo") String distNo,@Param("categoryCodeList") List<String> categoryCodeList,@Param("ipSocietyCode")String ipSocietyCode) ;

    List<DistDataCalcWorkIpRoy> getIpRoysByDistAndRetain(@Param("distNo") String distNo, @Param("retainId") Long retain);

    @Select("select distinct work_unique_key from dist_data_calc_work_ip_roy where retain_id = #{retainId} AND dist_roy  > 0")
    List<String> getWorkUniqueKeysByRetain( @Param("retainId") Long retainId);

    @Select("select * from dist_data_calc_work_ip_roy where dist_no = #{distNo}  and work_unique_key = #{workUniqueKey}")
    List<DistDataCalcWorkIpRoy> getIpRoysByWorkUniquekey(@Param("distNo") String distNo, @Param("workUniqueKey") String workUniqueKey);

    @Select("select * from dist_data_calc_work_ip_roy where dist_no = 'I194' and work_unique_key = '026-1207452931'")
    List<DistDataCalcWorkIpRoy> test();

    /**
     * 根据 retain_id 查询 work_unique_key、title_id 和 dist_roy 汇总
     * @param retainId retain_id
     * @return 包含 work_unique_key、title_id 和 total_dist_roy 的 Map 列表
     */
    List<Map<String, Object>> getWorkUniqueKeyAndDistRoyByRetainId(@Param("retainId") Long retainId);
}