<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.distdata.DistDataSnapshotWorkIpShareMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.distdata.DistDataSnapshotWorkIpShare">

    </resultMap>

    <insert id="insertDuplicateList" useGeneratedKeys="true" keyProperty="id" parameterType="tw.org.must.must.model.distdata.DistDataSnapshotWorkIpShare">
        INSERT INTO `must`.`dist_data_snapshot_work_ip_share`
        (
        `work_id`,
        `work_society_code`,
        `work_unique_key`,
        `dist_no`,
        `ip_name_no`,
        `ip_name_type`,
        `ip_base_no`,
        `ip_society_code`,
        `sd`,
        `adj_dist_no`,
        `dist_flag`,
        `group_indicator`,
        `work_ip_role`,
        `pa_name_no`,
        `work_ip_share`,
        `create_time`,
        `amend_time`,
        `pool_code`,
        `ip_right`,
        `org_ip_share`,
        `agr_no`,
        `right_type`,
        `ccid_claim_header_id`,
         `ip_name`)
        VALUES
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.workId },
            #{item.workSocietyCode },
            #{item.workUniqueKey },
            #{item.distNo },
            #{item.ipNameNo },
            #{item.ipNameType },
            #{item.ipBaseNo },
            #{item.ipSocietyCode },
            #{item.sd },
            #{item.adjDistNo },
            #{item.distFlag },
            #{item.groupIndicator },
            #{item.workIpRole },
            #{item.paNameNo },
            #{item.workIpShare },
            #{item.createTime },
            #{item.amendTime },
            #{item.poolCode },
            #{item.ipRight },
            #{item.orgIpShare },
            #{item.agrNo },
            #{item.rightType },
            #{item.ccidClaimHeaderId },
            #{item.ipName }
            )
        </foreach>
        on duplicate key update
        amend_time=values(amend_time),
        dist_no = values(dist_no),
        work_unique_key = values(work_unique_key),
        ip_name_no = values(ip_name_no),
        ip_name = values(ip_name),
        right_type = values(right_type),
        ccid_claim_header_id = values(ccid_claim_header_id),
        group_indicator = values (group_indicator)
    </insert>

</mapper>