<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.distdata.DistDataCalcWorkIpRoyMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">

    </resultMap>
    <select id="getIpSocietyCodeByDistNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select DISTINCT ip_society_code from dist_data_calc_work_ip_roy where dist_no = #{distNo} and ip_society_code not in ('000','099','161') AND dist_roy > 0
    </select>
    <select id="getIpBaseNoByDistNo" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT ip_base_no from dist_data_calc_work_ip_roy where dist_no = #{distNo} and ip_type = 'L' and ip_society_code = '161' and dist_roy &gt; 0
    </select>
    <select id="selectIpNameByDistNoAndIpSocietyCode" resultType="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">
        select distinct ip_name_no,ip_type
        from dist_data_calc_work_ip_roy
        where dist_no = #{distNo}  and work_unique_key in
        <foreach collection="workUniqueKeyList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getWorkUniqueKeyByDisNoAndIpSociety" resultType="java.lang.String">
         select distinct work_unique_key
        from dist_data_calc_work_ip_roy
        where dist_no = #{distNo} and ip_society_code = #{ipSocietyCode} and sd = 'N' and dist_roy > 0
    </select>

    <select id="getIpRoysByDistAndRetain" resultType="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">
        select * from dist_data_calc_work_ip_roy
        where dist_no = #{distNo}
          and work_unique_key in(
            select distinct work_unique_key
            from dist_data_calc_work_ip_roy
            where retain_id = #{retainId} AND dist_roy  > 0
        )
    </select>

<!--    <select id="getWorkUniqueKeysByDistAndRetain"  resultType="java.lang.String">-->
<!--        select distinct work_unique_key-->
<!--        from dist_data_calc_work_ip_roy-->
<!--        where retain_id = #{retainId} AND dist_roy  > 0-->
<!--    </select>-->

<!--    <select id="getIpRoysByWorkUniquekey" resultType="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">-->
<!--        select * from dist_data_calc_work_ip_roy-->
<!--        where dist_no = #{distNo}  and work_unique_key = #{workUniqueKey}-->
<!--    </select>-->

    <select id="getWorkUniqueKeyByDisNoAndIpBaseNo" resultType="java.lang.String">
        select distinct work_unique_key
        from dist_data_calc_work_ip_roy
        where dist_no = #{distNo} and ip_base_no = #{ipBaseNo} and sd = 'N' and dist_roy > 0
    </select>

    <select id="getDistDataCalcWorkIpRoyForWorkAndIPIList" resultType="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">
        select distinct pool_right
        from dist_data_calc_work_ip_roy
        where dist_no = #{distNo} and  work_unique_key = #{workUniqueKey}
    </select>
    <select id="getIpWithWorkList" resultType="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">
        SELECT * FROM dist_data_calc_work_ip_roy WHERE dist_no = #{distNo} AND work_unique_key = #{workUniqueKey}
    </select>
    <select id="getDistWorkPointEnquiry" resultType="tw.org.must.must.model.report.DistWorkPointEnquiry">
        SELECT
            t1.dist_no as distNo,
            t1.pool_code as poolCode,
            t1.category_code as categoryCode,
            t1.work_id as workNo,
            t1.work_society_code as workSoc,
            t1.source_work_code as workCode,
            IF(t2.title IS NULL OR t2.title = '', t2.title_en, t2.title) as workTitle,
            t1.is_dist as distributable,
            t4.ref_work_id AS otWorkNo,
            t4.ref_work_society AS otWorkSoc,
          IF(t5.title IS NULL OR t5.title = '', t5.title_en, t5.title) as otWorkTitle,
            t1.`usage`,
            t1.perform_time as performingDate,
            t1.cllick_number as frequency,
            t1.av_work_id as avWorkNo,
            t1.av_works_society_code as avWorkSoc,
            IF(t3.title IS NULL OR t3.title = '', t3.title_en, t3.title) as avWorkTitle,
            t1.duration_m as durMin,
            t1.duration_s as durSec,
            t1.gross_point as grossPoint,
            0 as sourceRefId
        FROM `dist_data_calc_work_point` t1
        LEFT JOIN wrk_work_title t2 on t1.work_title_id = t2.id
        LEFT JOIN wrk_work_title t3 on t1.av_title_id = t3.id
        LEFT JOIN wrk_work t4 on t1.work_unique_key = t4.work_unique_key
        LEFT JOIN wrk_work_title t5 on t5.work_unique_key = CONCAT(LPAD(t4.`ref_work_society`,3,'0'),'-',t4.`ref_work_id`)
        WHERE t1.distNo = #{distNo}
    </select>
    <select id="getDistributionNoUpa" resultType="tw.org.must.must.model.report.DistributionNoUpa">
        SELECT
        DISTINCT t1.ip_base_no AS ipBaseNo,
        t1.dist_no AS distNo,
        IF( t3.chinese_name IS NULL OR t3.chinese_name = '', t3.`name`, t3.chinese_name ) as `name`,
        t4.type as membership
        FROM
        `dist_data_calc_work_ip_roy` t1
        LEFT JOIN dist_calc_retain t2 ON t2.id = t1.retain_id
        LEFT JOIN mbr_ip_name t3 ON t3.ip_name_no = t1.ip_name_no
        LEFT JOIN mbr_member_membership t4 ON t4.ip_base_no = t1.ip_base_no
        WHERE
        t4.valid_from &lt;  CURRENT_DATE AND t4.valid_to &gt; CURRENT_DATE
        AND ( t2.upa_amt IS NULL OR t2.upa_amt &lt;= 0 )
        AND t1.dist_no = #{distNo}
    </select>
   <!-- <select id="getCalcWorkIpRoyGroupByFid"  resultType="java.util.Map">
        select file_base_id as fid,sum(ifnull(dist_roy,0)) from dist_data_calc_work_ip_roy where dist_no = #{distNo} group by file_base_id
    </select>-->
    <select id="getTotalNetPointGroupByCategoryCode" resultType="tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy">
        select category_code, sum(net_point) as net_point from dist_data_calc_work_ip_roy where dist_no = #{distNo} and category_code in
        <foreach collection="categoryCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by category_code
    </select>

    <select id="getIsHasRoyByMember" resultType="java.lang.Long">
        select id from dist_data_calc_work_ip_roy where dist_no = #{distNo} and ip_base_no = #{ipBaseNo} and category_code in
        <foreach collection="categoryCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and dist_roy > 0 limit 1
    </select>

    <select id="getIsHasRoyBySoc" resultType="java.lang.Long">
        select id from dist_data_calc_work_ip_roy where dist_no = #{distNo} and ip_society_code = #{ipSocietyCode} and category_code in
        <foreach collection="categoryCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and dist_roy > 0 limit 1
    </select>

    <select id="getWorkUniqueKeyAndDistRoyByRetainId" resultType="java.util.Map">
        SELECT work_unique_key, MAX(title_id) AS title_id, SUM(dist_roy) AS total_dist_roy
        FROM dist_data_calc_work_ip_roy
        WHERE retain_id = #{retainId}
        GROUP BY work_unique_key
        HAVING SUM(dist_roy) > 0
    </select>

</mapper>