package tw.org.must.must.model.report;

import java.io.Serializable;
import java.util.List;

public class WorkData4MOverSeas implements Serializable {

    private static final long serialVersionUID = 6976596094700288822L;

    private String workTitle; // 显示格式【中文标题】（【英文标题】），如果没有中文标题则【英文标题】
    private String refWorkTitle;
    private String workNo; // work id/work soc格式，如果iswc不为空则该栏位则填写iswc
    private List<WorkData> workDataList;
    private List<WorkIpRoy> workIpRoyList;
    private String oldDistNo;
    private List<WorkIpRoy> workIpshareList;
    private String originaltitle;
    private String status;
    private String ipNameNo;
    private String society;
    private String tv;
    private String radio;
    private String concert;
    private String karaoke;
    private String general;
    private String others;
    private String workTotal;

    public String getTv() {
        return tv;
    }

    public void setTv(String tv) {
        this.tv = tv;
    }

    public String getRadio() {
        return radio;
    }

    public void setRadio(String radio) {
        this.radio = radio;
    }

    public String getConcert() {
        return concert;
    }

    public void setConcert(String concert) {
        this.concert = concert;
    }

    public String getKaraoke() {
        return karaoke;
    }

    public void setKaraoke(String karaoke) {
        this.karaoke = karaoke;
    }

    public String getGeneral() {
        return general;
    }

    public void setGeneral(String general) {
        this.general = general;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }

    public String getWorkTotal() {
        return workTotal;
    }

    public void setWorkTotal(String workTotal) {
        this.workTotal = workTotal;
    }

    public String getOldDistNo() {
        return oldDistNo;
    }

    public void setOldDistNo(String oldDistNo) {
        this.oldDistNo = oldDistNo;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getRefWorkTitle() {
        return refWorkTitle;
    }

    public void setRefWorkTitle(String refWorkTitle) {
        this.refWorkTitle = refWorkTitle;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public List<WorkIpRoy> getWorkIpRoyList() {
        return workIpRoyList;
    }

    public void setWorkIpRoyList(List<WorkIpRoy> workIpRoyList) {
        this.workIpRoyList = workIpRoyList;
    }


    public String getOriginaltitle() {
        return originaltitle;
    }

    public void setOriginaltitle(String originaltitle) {
        this.originaltitle = originaltitle;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getSociety() {
        return society;
    }

    public void setSociety(String society) {
        this.society = society;
    }

    public List<WorkIpRoy> getWorkIpshareList() {
        return workIpshareList;
    }

    public void setWorkIpshareList(List<WorkIpRoy> workIpshareList) {
        this.workIpshareList = workIpshareList;
    }
}
