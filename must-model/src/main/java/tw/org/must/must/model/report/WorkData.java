package tw.org.must.must.model.report;

import java.io.Serializable;
import java.util.List;

public class WorkData implements Serializable {

    private static final long serialVersionUID = 6976596094700288822L;

    private String workTitle; // 显示格式【中文标题】（【英文标题】），如果没有中文标题则【英文标题】
    private String refWorkTitle;
    private String workNo; // work id/work soc格式，如果iswc不为空则该栏位则填写iswc
    private List<WorkIpRoy> workIpRoyList;
    private String oldDistNo;
    private String sd;
    private String originaltitle;
    private String others;

    public String getOldDistNo() {
        return oldDistNo;
    }

    public void setOldDistNo(String oldDistNo) {
        this.oldDistNo = oldDistNo;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getRefWorkTitle() {
        return refWorkTitle;
    }

    public void setRefWorkTitle(String refWorkTitle) {
        this.refWorkTitle = refWorkTitle;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public List<WorkIpRoy> getWorkIpRoyList() {
        return workIpRoyList;
    }

    public void setWorkIpRoyList(List<WorkIpRoy> workIpRoyList) {
        this.workIpRoyList = workIpRoyList;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getOriginaltitle() {
        return originaltitle;
    }

    public void setOriginaltitle(String originaltitle) {
        this.originaltitle = originaltitle;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }
}
