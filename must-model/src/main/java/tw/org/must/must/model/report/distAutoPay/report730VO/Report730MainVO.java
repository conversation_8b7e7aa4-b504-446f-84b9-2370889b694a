package tw.org.must.must.model.report.distAutoPay.report730VO;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class Report730MainVO implements Serializable {
    /**表头*/
    // 日期
    private String date;
    private String autoPayNo;
    private String memberName;
    private String ipBaseNo;
    private String paNameNo;

    // 内容
    private List<Report730Sub> report730SubList;


    /**表尾*/
    // 权利金总计
    private BigDecimal totalRoyalties;
    // 管理费/行政费
    private BigDecimal totalCommission;
    // 所得税
    private BigDecimal totalTax;
    // 分配支付额
    private BigDecimal distributionPayment;
    // 银行手续费
    private BigDecimal handingCharge;
    // 净支付额
    private BigDecimal netPayment;

    //subTwo内容
    private List<Report730SubTwo> report730SubTwoList;


    //sub报表内部类
    @Data
    public static class Report730Sub implements Serializable{


        /**child位置*/
        private List<Report730Child> report730ChildList;

        // 权利金总计
        private String royaltiesAmount;

        /***/
        private Report730Template report730Template;

        // 会员名称=====>memberName
        private String subData;
        private String json;
        private String bankName;
        private String adjRoyalAmount;
        // 立据人=====>memberName


        // template路径
        private String templatePath;

        @Data
        public static class Report730Child implements Serializable{
            // 权利金类型 权利金/海外权利金
            private String royaltiesType;
            private List<Report730ChildTwo> report730ChildTwoList;

            @Data
            public static class Report730ChildTwo implements Serializable{
                private String distNo;
                private Report730ChildThird report730ChildThird;

                // 730Child_third 路径
                private String thirdPath;


                @Data
                public static class Report730ChildThird implements Serializable{

                    // 权利金描述
                    private String firstDescribe;
                    //权利金
                    private BigDecimal royaltyAmount;
                    //权利金调整
                    private String royaltiesAdj;

                    //管理费比例
                    private String commissionRate;
                    //管理费
                    private String commissionAmount;

                    //所得税比例
                    private String taxableRate;
                    //所得税
                    private String taxableAmount;


                    /**自动转账UPA    电汇*/
                    //加扣管理费比例
                    private String additionalCommissionRate;
                    //加扣管理费 针对准会员加收的附加管理费
                    private String additionalCommissionAmount;
                    //所得税调整
                    private String deduction;

                    // 支票+相信音乐类型 营业税
                    private String businessRate;
                    private String businessAmount;

                    // 权利金小计
                    private String royaltiesCountAmount;

                }
            }
        }

        @Data
        public static class Report730Template implements Serializable{
            // 权利金分发金额
            private String adjRoyalAmount;
            /**自动转账    自动转账UPA*/
            //银行名称===>bankName
            private String bankName;
            //户口名称===>memberName
            //户口号码
            private String accountNo;

            /**电汇*/
            // 货币
            private String currency;
            // 收款人银行名称===》bankName
            // 收款人===》memberName
            // 收款人号码===》accountNo
            //收款人银行地址
            private String bankAddress;

            /**支票  团体  相信音乐*/
            // 抬头人姓名 ===》 memberName

        }
    }
    //sub_two报表内部类
    @Data
    public static class Report730SubTwo implements Serializable{
        private String distNo;
        private String totalAmount;
    }
}
