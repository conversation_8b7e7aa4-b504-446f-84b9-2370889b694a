package tw.org.must.must.model.report;

import java.io.Serializable;
import java.util.List;

public class FieWorkData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String remitWorkTitle;
    private String remitWorkNo;
    private List<WorkIpRoy> feeInErrorList;

    private String workTitle;
    private String workNo;
    private List<WorkIpRoy> workIpRoyList;

    // 添加模板需要的字段
    private String originaltitle;
    private String sd;
    private String total;
    private String status;
    private String ipNameNo;
    private String society;
    private String tv;
    private String radio;
    private String concert;
    private String karaoke;
    private String general;
    private String others;
    private String workTotal;
    private String originalWorkNo;


    public String getRemitWorkTitle() {
        return remitWorkTitle;
    }

    public void setRemitWorkTitle(String remitWorkTitle) {
        this.remitWorkTitle = remitWorkTitle;
    }

    public String getRemitWorkNo() {
        return remitWorkNo;
    }

    public void setRemitWorkNo(String remitWorkNo) {
        this.remitWorkNo = remitWorkNo;
    }

    public List<WorkIpRoy> getFeeInErrorList() {
        return feeInErrorList;
    }

    public void setFeeInErrorList(List<WorkIpRoy> feeInErrorList) {
        this.feeInErrorList = feeInErrorList;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public List<WorkIpRoy> getWorkIpRoyList() {
        return workIpRoyList;
    }

    public void setWorkIpRoyList(List<WorkIpRoy> workIpRoyList) {
        this.workIpRoyList = workIpRoyList;
    }

    public String getOriginaltitle() {
        return originaltitle;
    }

    public void setOriginaltitle(String originaltitle) {
        this.originaltitle = originaltitle;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getSociety() {
        return society;
    }

    public void setSociety(String society) {
        this.society = society;
    }

    public String getTv() {
        return tv;
    }

    public void setTv(String tv) {
        this.tv = tv;
    }

    public String getRadio() {
        return radio;
    }

    public void setRadio(String radio) {
        this.radio = radio;
    }

    public String getConcert() {
        return concert;
    }

    public void setConcert(String concert) {
        this.concert = concert;
    }

    public String getKaraoke() {
        return karaoke;
    }

    public void setKaraoke(String karaoke) {
        this.karaoke = karaoke;
    }

    public String getGeneral() {
        return general;
    }

    public void setGeneral(String general) {
        this.general = general;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }

    public String getWorkTotal() {
        return workTotal;
    }

    public void setWorkTotal(String workTotal) {
        this.workTotal = workTotal;
    }

    public String getOriginalWorkNo() {
        return originalWorkNo;
    }

    public void setOriginalWorkNo(String originalWorkNo) {
        this.originalWorkNo = originalWorkNo;
    }
}
