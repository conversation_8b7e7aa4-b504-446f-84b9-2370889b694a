package tw.org.must.must.model.report;

import java.io.Serializable;
import java.util.List;

public class FieWorkData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String remitWorkTitle;
    private String remitWorkNo;
    private List<WorkIpRoy> feeInErrorList;

    private String workTitle;
    private String workNo;
    private List<WorkIpRoy> workIpRoyList;


    public String getRemitWorkTitle() {
        return remitWorkTitle;
    }

    public void setRemitWorkTitle(String remitWorkTitle) {
        this.remitWorkTitle = remitWorkTitle;
    }

    public String getRemitWorkNo() {
        return remitWorkNo;
    }

    public void setRemitWorkNo(String remitWorkNo) {
        this.remitWorkNo = remitWorkNo;
    }

    public List<WorkIpRoy> getFeeInErrorList() {
        return feeInErrorList;
    }

    public void setFeeInErrorList(List<WorkIpRoy> feeInErrorList) {
        this.feeInErrorList = feeInErrorList;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public List<WorkIpRoy> getWorkIpRoyList() {
        return workIpRoyList;
    }

    public void setWorkIpRoyList(List<WorkIpRoy> workIpRoyList) {
        this.workIpRoyList = workIpRoyList;
    }
}
