package tw.org.must.must.model.report;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class DistReportFieData implements Serializable {

    private static final long serialVersionUID = -4930794956528094276L;

    private Header header;
    private List<FieWorkData> workData;
    private Summary summary;

    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Summary getSummary() {
        return summary;
    }

    public void setSummary(Summary summary) {
        this.summary = summary;
    }

    public List<FieWorkData> getWorkData() {
        return workData;
    }

    public void setWorkData(List<FieWorkData> workData) {
        this.workData = workData;
    }

    public static class Header {
        private String distributionNo;
        private Date date;
        private String mustMemberName;
        private String ipBaseNo;
        private String paNameNo;
        private String memberNo;

        public String getDistributionNo() {
            return distributionNo;
        }

        public void setDistributionNo(String distributionNo) {
            this.distributionNo = distributionNo;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public String getMustMemberName() {
            return mustMemberName;
        }

        public void setMustMemberName(String mustMemberName) {
            this.mustMemberName = mustMemberName;
        }

        public String getIpBaseNo() {
            return ipBaseNo;
        }

        public void setIpBaseNo(String ipBaseNo) {
            this.ipBaseNo = ipBaseNo;
        }

        public String getPaNameNo() {
            return paNameNo;
        }

        public void setPaNameNo(String paNameNo) {
            this.paNameNo = paNameNo;
        }

        public String getMemberNo() {
            return memberNo;
        }

        public void setMemberNo(String memberNo) {
            this.memberNo = memberNo;
        }
    }
}
