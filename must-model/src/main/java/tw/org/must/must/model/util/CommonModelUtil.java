package tw.org.must.must.model.util;

import tw.org.must.must.model.wrk.WrkWork;

import java.util.Calendar;

public class CommonModelUtil {

    public static WrkWork createWrkWork(String workUniqueKey, String  genre, Long refWorkId, Integer refWorkSoc){
        WrkWork wrkWork = new WrkWork();
        wrkWork.setGenre(genre);
        wrkWork.setWorkUniqueKey(workUniqueKey);
        if(null != refWorkId){
            wrkWork.setRefWorkId(refWorkId);
        }
        if(null != refWorkSoc){
            wrkWork.setRefWorkSociety(refWorkSoc);
        }
        return wrkWork;
    }

    public static String getYearByDistNo(String distNo){
        String year2 = distNo.substring(1,3);
        Calendar date = Calendar.getInstance();
        String year1 = String.valueOf(date.get(Calendar.YEAR)).substring(0,2);
        String year = year1+year2;
        return year;
    }

}
