package tw.org.must.must.model.report;

import tw.org.must.must.common.base.BaseEntity;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "dist_overseas_ip_not_in_work")
public class DistOverseasCheckingIpNotInWork extends BaseEntity {

    @Column(name = "dist_no")
    private String distNo;
    @Column(name = "file_base_id")
    private String adjNo;
    /**
     * list_overseas_receipt_details.dist_order_number
     */
    @Column(name = "dist_order_number")
    private Integer overseasDistSeq;

    /**
     * O 分配固定填O
     */
    @Column(name = "pool_code")
    private String poolCode = "O";

    @Column(name = "source_work_code")
    private String sourceWorkCode ;

    @Column(name = "original_title")
    private String originalTitle ;

    @Column(name = "ip_name_no")
    private String ipNameNo ;

    @Column(name = "pa_name_no")
    private String paNameNo ;

    @Column(name = "name")
    private String name ;

    @Column(name = "work_ip_role")
    private String workIpRole ;

    @Column(name = "ip_society_code")
    private String societyCode ;

    @Column(name = "ip_share")
    private BigDecimal ipShare ;

    @Column(name = "reject_code")
    private String fieCode ;

    @Column(name = "dist_ip_share")
    private BigDecimal distIpShare ;

    @Column(name = "file_mapping_id")
    private Long amountSeqNo ;

    @Column(name = "data_unique_key")
    private String distinctWorknum ;

    @Column(name = "match_work_id")
    private Long matchWorkId ;

    @Column(name = "match_work_num_society_code")
    private Integer matchWorknumSocietyCode ;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getAdjNo() {
        return adjNo;
    }

    public void setAdjNo(String adjNo) {
        this.adjNo = adjNo;
    }

    public Integer getOverseasDistSeq() {
        return overseasDistSeq;
    }

    public void setOverseasDistSeq(Integer overseasDistSeq) {
        this.overseasDistSeq = overseasDistSeq;
    }

    public String getPoolCode() {
        return poolCode;
    }

    public void setPoolCode(String poolCode) {
        this.poolCode = poolCode;
    }

    public String getSourceWorkCode() {
        return sourceWorkCode;
    }

    public void setSourceWorkCode(String sourceWorkCode) {
        this.sourceWorkCode = sourceWorkCode;
    }

    public String getOriginalTitle() {
        return originalTitle;
    }

    public void setOriginalTitle(String originalTitle) {
        this.originalTitle = originalTitle;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWorkIpRole() {
        return workIpRole;
    }

    public void setWorkIpRole(String workIpRole) {
        this.workIpRole = workIpRole;
    }

    public String getSocietyCode() {
        return societyCode;
    }

    public void setSocietyCode(String societyCode) {
        this.societyCode = societyCode;
    }

    public BigDecimal getIpShare() {
        return ipShare;
    }

    public void setIpShare(BigDecimal ipShare) {
        this.ipShare = ipShare;
    }

    public String getFieCode() {
        return fieCode;
    }

    public void setFieCode(String fieCode) {
        this.fieCode = fieCode;
    }

    public BigDecimal getDistIpShare() {
        return distIpShare;
    }

    public void setDistIpShare(BigDecimal distIpShare) {
        this.distIpShare = distIpShare;
    }

    public Long getAmountSeqNo() {
        return amountSeqNo;
    }

    public void setAmountSeqNo(Long amountSeqNo) {
        this.amountSeqNo = amountSeqNo;
    }

    public String getDistinctWorknum() {
        return distinctWorknum;
    }

    public void setDistinctWorknum(String distinctWorknum) {
        this.distinctWorknum = distinctWorknum;
    }

    public Long getMatchWorkId() {
        return matchWorkId;
    }

    public void setMatchWorkId(Long matchWorkId) {
        this.matchWorkId = matchWorkId;
    }

    public Integer getMatchWorknumSocietyCode() {
        return matchWorknumSocietyCode;
    }

    public void setMatchWorknumSocietyCode(Integer matchWorknumSocietyCode) {
        this.matchWorknumSocietyCode = matchWorknumSocietyCode;
    }

    public DistOverseasCheckingIpNotInWork convertFromMapping(ListMatchDataOverseasMapping listMatchDataOverseasMapping) {

        DistOverseasCheckingIpNotInWork distOverseasCheckingIpNotInWork = new DistOverseasCheckingIpNotInWork();
        distOverseasCheckingIpNotInWork.setId(listMatchDataOverseasMapping.getId());
        distOverseasCheckingIpNotInWork.setAdjNo(listMatchDataOverseasMapping.getFileBaseId() + "");

        return distOverseasCheckingIpNotInWork;
    }
}
