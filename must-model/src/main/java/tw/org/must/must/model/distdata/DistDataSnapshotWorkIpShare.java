package tw.org.must.must.model.distdata;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Table;

import tw.org.must.must.common.base.BaseEntity;

@Table(name = "`dist_data_snapshot_work_ip_share`")
public class DistDataSnapshotWorkIpShare extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;
    
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 
     */
    @Column(name = "`ip_name_no`")
    private String ipNameNo;

    /**
     * 
     */
    @Column(name = "`ip_name_type`")
    private String ipNameType;

    /**
     * 
     */
    @Column(name = "`ip_base_no`")
    private String ipBaseNo;

    /**
     * 
     */
    @Column(name = "`ip_society_code`")
    private String ipSocietyCode;

    /**
     * 
     */
    @Column(name = "`sd`")
    private String sd;

    /**
     * 
     */
    @Column(name = "`adj_dist_no`")
    private String adjDistNo;

    /**
     * 
     */
    @Column(name = "`dist_flag`")
    private String distFlag;

    /**
     * 
     */
    @Column(name = "`group_indicator`")
    private String groupIndicator;

    /**
     * 
     */
    @Column(name = "`work_ip_role`")
    private String workIpRole;

    /**
     * 
     */
    @Column(name = "`pa_name_no`")
    private String paNameNo;

    /**
     * 
     */
    @Column(name = "`work_ip_share`")
    private BigDecimal workIpShare;

    /**
     * 
     */
    @Column(name = "`pool_code`")
    private String poolCode;

    /**
     * 
     */
    @Column(name = "`ip_right`")
    private String ipRight;

    /**
     * 
     */
    @Column(name = "`org_ip_share`")
    private String orgIpShare;

    /**
     * 
     */
    @Column(name = "`agr_no`")
    private String agrNo;
    
    @Column(name = "`right_type`")
    private String rightType;
    
    @Column(name = "ccid_claim_header_id")
    private Long ccidClaimHeaderId;

    @Column(name = "`ip_name`")
    private String ipName ;

    public Long getCcidClaimHeaderId() {
		return ccidClaimHeaderId;
	}

	public void setCcidClaimHeaderId(Long ccidClaimHeaderId) {
		this.ccidClaimHeaderId = ccidClaimHeaderId;
	}

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getIpNameType() {
        return ipNameType;
    }

    public void setIpNameType(String ipNameType) {
        this.ipNameType = ipNameType;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getIpSocietyCode() {
        return ipSocietyCode;
    }

    public void setIpSocietyCode(String ipSocietyCode) {
        this.ipSocietyCode = ipSocietyCode;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getAdjDistNo() {
        return adjDistNo;
    }

    public void setAdjDistNo(String adjDistNo) {
        this.adjDistNo = adjDistNo;
    }

    public String getDistFlag() {
        return distFlag;
    }

    public void setDistFlag(String distFlag) {
        this.distFlag = distFlag;
    }

    public String getGroupIndicator() {
        return groupIndicator;
    }

    public void setGroupIndicator(String groupIndicator) {
        this.groupIndicator = groupIndicator;
    }

    public String getWorkIpRole() {
        return workIpRole;
    }

    public void setWorkIpRole(String workIpRole) {
        this.workIpRole = workIpRole;
    }

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public BigDecimal getWorkIpShare() {
        return workIpShare;
    }

    public void setWorkIpShare(BigDecimal workIpShare) {
        this.workIpShare = workIpShare;
    }

    public String getPoolCode() {
        return poolCode;
    }

    public void setPoolCode(String poolCode) {
        this.poolCode = poolCode;
    }

    public String getIpRight() {
        return ipRight;
    }

    public void setIpRight(String ipRight) {
        this.ipRight = ipRight;
    }

    public String getOrgIpShare() {
        return orgIpShare;
    }

    public void setOrgIpShare(String orgIpShare) {
        this.orgIpShare = orgIpShare;
    }

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public String getRightType() {
		return rightType;
	}

	public void setRightType(String rightType) {
		this.rightType = rightType;
	}

    public String getIpName() {
        return ipName;
    }

    public void setIpName(String ipName) {
        this.ipName = ipName;
    }
}