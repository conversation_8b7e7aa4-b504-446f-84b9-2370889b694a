server:
  port: 8080
  tomcat:
    max-swallow-size: -1
    servlet:
      context-path: /
    compression:
      enabled: true
      min-response-size: 1024
      mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
spring:
  datasource:
    master:
      url: jdbc:mysql://**************:3306/must?useUnicode=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&zeroDateTimeBehavior=convertToNull&useSSL=false
      username: must
      password: Mdb1933
    slave1:
      url: ****************************************************************************************************************************************************
      username: must
      password: Mdb1933
    slave2:
      url: ****************************************************************************************************************************************************
      username: must
      password: Mdb1933
    crd:
      url: jdbc:mysql://**************:3306/crd?useUnicode=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&zeroDateTimeBehavior=convertToNull&useSSL=false
      username: must
      password: Mdb1933
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      filters: stat
      initial-size: 10
      min-idle: 20
      max-active: 500
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 'x'
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
  datasource1:
      url: jdbc:oracle:thin:@************:1521/diva161.must.org.tw
      username: system
      password: diva161sys12
      driver-class-name: oracle.jdbc.driver.OracleDriver
  cache:
    type: redis
  redis:
    host: **************
    port: 6379
    timeout: 10000
    jedis:
      pool:
        max-active: 20
        max-wait: -1
        max-idle: 10
        min-idle: 0
        test-on-borrow: true
    database: 1
  mvc:
    favicon:
      enabled: false
    async:
      request-timeout: 300000
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  task:
    scheduling:
      pool:
        size: 100
  quartz:
    auto-startup: false
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    startup-delay: 60
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          dataSource:
            masterDataSource:
              driver: com.mysql.cj.jdbc.Driver
              URL: ****************************************************************************************************************************************************
              user: must
              password: Mdb1933
          scheduler:
            instanceId: AUTO #默认主机名和时间戳生成实例ID,可以是任何字符串，但对于所有调度程序来说，必须是唯一的 对应qrtz_scheduler_state INSTANCE_NAME字段
            instanceName: clusteredScheduler
          jobStore:
            dataSource: masterDataSource
            acquireTriggersWithinLock: true
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #我们仅为数据库制作了特定于数据库的代理
            useProperties: false #以指示JDBCJobStore将JobDataMaps中的所有值都作为字符串，因此可以作为名称 - 值对存储而不是在BLOB列中以其序列化形式存储更多复杂的对象。从长远来看，这是更安全的，因为您避免了将非String类序列化为BLOB的类版本问题。
            tablePrefix: QRTZ_ #数据库表前缀
            misfireThreshold: 60000 #在被认为“失火”之前，调度程序将“容忍”一个Triggers将其下一个启动时间通过的毫秒数。默认值（如果您在配置中未输入此属性）为60000（60秒）。
            clusterCheckinInterval: 5000 #设置此实例“检入”*与群集的其他实例的频率（以毫秒为单位）。影响检测失败实例的速度。
            isClustered: false #打开群集功能,集群模式需要在多台服务器上做时间同步或者使用zookeeper去解决
          threadPool: #连接池
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 20
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
logging:
  config: classpath:logback.xml
  level:
    tw.org.must.must: debug
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
pagehelper:
  page-size-zero: true
  reasonable: false
swagger2:
  enable: true
elasticSearch:
  host: **************, **************, **************
  port: 9200
  client:
    connectNum: 10
    connectPerRoute: 50
async-xml-parser:
  core-pool-size: 5
  max-pool-size: 10
queue:
  size:
    ddex:
      resource: 10000
      release: 10000
      release-transaction: 10000
  consume:
    core-pool-size: 10
    max-pool-size: 20
list:
  file:
    path: /home/<USER>/webFile/upload/listFile
    overseasPath: /home/<USER>/webFile/upload/overseasList
    claimPath: /home/<USER>/webFile/upload/claimList
    ccidFilePath: /home/<USER>/webFile/upload/ccid/filePath
    ccidFileOutPath: /home/<USER>/webFile/upload/ccid/fileOutPath
    exportSqlPath: /home/<USER>/webFile/upload/exportSql
    uploadTempPath: /home/<USER>/webFile/upload/temp
    exportPath: /home/<USER>/webFile/upload/export
    errorPath: /home/<USER>/webFile/upload/error
video-path:
  upload_video: /home/<USER>/money/upload/upload_video
root: /home/<USER>/webFile/upload/sysJobLog
ftp-path:
  ddex: /home/<USER>/webFile/upload/ddex
  ipi: /home/<USER>/webFile/upload/ipi
  cwr: /home/<USER>/webFile/upload/cwr
  avr: /home/<USER>/webFile/upload/avr
schedule:
  start: true
iswc:
  subscriptionKey: fbc73474556a45c0889ddc0cce321321
  submissionBatch: ***********************************/submission/batch
  log: /home/<USER>/webFile/upload/iswc/logPath
sharding:
  open: false
 