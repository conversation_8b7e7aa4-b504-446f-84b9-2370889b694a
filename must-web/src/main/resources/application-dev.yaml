server:
  port: 6656
  tomcat:
    max-swallow-size: -1
  servlet:
    context-path: /
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
spring:
#  rabbitmq:
#    host: ***********
#    port: 5672
#    username: must
#    password: firstbrave
#    # 开启消息确认机制 confirm 异步
#    publisher-confirm-type: correlated
#    listener:
#      direct:
#        # 消息开启手动确认
#        acknowledge-mode: manual
#    publisher-returns: true
#    main:
#      allow-bean-definition-overriding: true
  datasource:
    master:
      url: **************************************************************************
      username: must
      password: firstbrave

    slave1:
      url: **************************************************************************
      username: must
      password: firstbrave
    slave2:
      url: **************************************************************************
      username: must
      password: firstbrave

    crd:
      url: ********************************************************************************************
      username: must
      password: firstbrave

    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      filters: stat
      initial-size: 10
      min-idle: 20
      max-active: 500
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 'x'
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
  datasource1:
    url: *******************************************************
    username: system
    password: diva161sys12
    driver-class-name: oracle.jdbc.driver.OracleDriver
    type: com.alibaba.druid.pool.DruidDataSource
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    useGlobalDataSourceStat: true
  cache:
    type: redis
  redis:
    host: ***********
    port: 6379
    password: firstbrave
    timeout: 10000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
    database: 1
  mvc:
    favicon:
      enabled: false
    async:
      request-timeout: 300000
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  task:
    scheduling:
      pool:
        size: 50
  quartz:
    auto-startup: false
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    startup-delay: 60
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          dataSource:
            masterDataSource:
              driver: com.mysql.cj.jdbc.Driver
              URL: **************************************************************************
              user: must
              password: firstbrave
          scheduler:
            instanceId: AUTO #默认主机名和时间戳生成实例ID,可以是任何字符串，但对于所有调度程序来说，必须是唯一的 对应qrtz_scheduler_state INSTANCE_NAME字段
            instanceName: clusteredScheduler
          jobStore:
            dataSource: masterDataSource
            acquireTriggersWithinLock: true
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #我们仅为数据库制作了特定于数据库的代理
            useProperties: false #以指示JDBCJobStore将JobDataMaps中的所有值都作为字符串，因此可以作为名称 - 值对存储而不是在BLOB列中以其序列化形式存储更多复杂的对象。从长远来看，这是更安全的，因为您避免了将非String类序列化为BLOB的类版本问题。
            tablePrefix: QRTZ_ #数据库表前缀
            misfireThreshold: 60000 #在被认为“失火”之前，调度程序将“容忍”一个Triggers将其下一个启动时间通过的毫秒数。默认值（如果您在配置中未输入此属性）为60000（60秒）。
            clusterCheckinInterval: 5000 #设置此实例“检入”*与群集的其他实例的频率（以毫秒为单位）。影响检测失败实例的速度。
            isClustered: false #打开群集功能,集群模式需要在多台服务器上做时间同步或者使用zookeeper去解决
          threadPool: #连接池
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 20
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
logging:
  config: classpath:logback-dev.xml
  level:
    tw.org.must.must: debug
    tw.org.must.must.mapper: debug
# The default slf4j seems to work just fine, allowing us to filter log messages properly
#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
pagehelper:
  page-size-zero: true
  reasonable: false
swagger2:
  enable: true
elasticSearch:
  host: ***********
  port: 9200
  client:
    connectNum: 10
    connectPerRoute: 50
async-xml-parser:
  core-pool-size: 5
  max-pool-size: 10
queue:
  size:
    ddex:
      resource: 10000
      release: 10000
      release-transaction: 10000
  consume:
    core-pool-size: 10
    max-pool-size: 20
matching-list:
  api: http://***********:9998
ftp-path:
  ddex: C:\Users\<USER>\Desktop\解析\DDEX\201601
  ipi: C:\n
  cwr: F:\Test\cwr\
  avr: C:\n
video-path:
  upload_video: c:\n\upload_video
list:
  file:
    path: D:\Users\admin\Desktop\mustUpload\list
    overseasPath: F:\Test\olist\opload
    claimPath: C:\Users\<USER>\Desktop\mustUpload\claimList
    ccidFilePath: C:\Users\<USER>\Desktop\mustUpload\ccid\filePath
    ccidFileOutPath: C:\Users\<USER>\Desktop\mustUpload\ccid\fileOutPath
    exportSqlPath: F:\must\download\exportSql
    uploadTempPath: D:\Test\pdf
    exportPath: D:\Test\export
    errorPath: D:\Test\error
root: D:/log
schedule:
  start: false
iswc:
  subscriptionKey: e00d4a2fcc7146e5901847acf82123f0
  submissionBatch: **********************************/submission/batch
  log: C:\Users\<USER>\Desktop\iswc
sharding:
  open: false
  claim_result_ccid:
    actual-data-nodes: claim_result_ccid_$->{161..163},claim_result_ccid_$->{165..170}
    column: header_id
    start-index: 182

