<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.5.final using JasperReports Library version 6.21.5-74d586df47b25dbd05bd0957999819196e59934a  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DistAutoPay730_child" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="6a5a2365-15da-4757-a88a-845c985b874a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="report730ChildTwoList" class="java.lang.Object"/>
	<field name="royaltiesType" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="53">
			<textField>
				<reportElement x="0" y="0" width="170" height="20" uuid="c7f51ea0-93d4-46a8-a53e-e0f22d1712f6"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{royaltiesType}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="0" y="25" width="572" height="24" uuid="b7b19ce0-ef38-45be-b14f-95b3937c642c"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("report730ChildTwoList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"distAutoPays/report730/DistAutoPay730_childTwo.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
